"""Child Growth Tracker - Ultra-Optimized Pediatric Monitoring Tool"""

import json, base64
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field
from io import BytesIO
import sys, os

# Import core growth tracker
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from growth_tracker import ChildGrowthTracker

# --- Elite Request Models ---
class ChildProfileRequest(BaseModel):
    name: str = Field(..., description="Child's name")
    birth_date: str = Field(..., description="Birth date in YYYY-MM-DD format")
    gender: str = Field(..., description="Child's gender (male/female)")

class GrowthMeasurementRequest(BaseModel):
    date: str = Field(..., description="Measurement date in YYYY-MM-DD format")
    height: float = Field(..., description="Height in centimeters")
    weight: float = Field(..., description="Weight in kilograms")

class MilestoneRequest(BaseModel):
    milestone: str = Field(..., description="Milestone description")
    date: str = Field(..., description="Achievement date in YYYY-MM-DD format")

class VaccinationRequest(BaseModel):
    vaccine: str = Field(..., description="Vaccine name")
    date: str = Field(..., description="Vaccination date in YYYY-MM-DD format")

class GrowthAnalysisRequest(BaseModel):
    include_recommendations: bool = Field(default=True, description="Include health recommendations")

class GrowthPredictionRequest(BaseModel):
    target_age_months: float = Field(..., description="Target age in months for prediction")

class PhotoMemoryRequest(BaseModel):
    image_base64: str = Field(..., description="Base64 encoded image data")
    caption: str = Field(..., description="Photo caption")
    date: str = Field(..., description="Photo date in YYYY-MM-DD format")

class DoctorNoteRequest(BaseModel):
    date: str = Field(..., description="Visit date in YYYY-MM-DD format")
    note: str = Field(..., description="Doctor's note")
    doctor: str = Field(default="", description="Doctor's name")

class ReminderRequest(BaseModel):
    reminder_type: str = Field(..., description="Type of reminder")
    due_date: str = Field(..., description="Due date in YYYY-MM-DD format")
    notes: str = Field(default="", description="Additional notes")

class FullReportRequest(BaseModel):
    include_chart: bool = Field(default=True, description="Include growth chart")

# --- Elite Tool Class ---
class ChildGrowthTrackerTool:
    """Child growth tracker with minimal code, maximum pediatric functionality"""
    
    def __init__(self):
        self.name = "child_growth_tracker_tool"
        self.description = "Elite comprehensive child development tracking system"
        self.tracker = ChildGrowthTracker()
        
    def create_child_profile(self, request: ChildProfileRequest) -> Dict[str, Any]:
        """Profile creation"""
        try:
            result = self.tracker.create_child_profile(request.name, request.birth_date, request.gender)
            return {"success": True, "message": f"Profile created for {request.name}", "profile": result}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_growth_measurement(self, request: GrowthMeasurementRequest) -> Dict[str, Any]:
        """Growth measurement with automatic analysis"""
        try:
            result = self.tracker.add_growth_measurement(request.date, request.height, request.weight)
            percentiles = self.tracker.calculate_growth_percentiles()
            return {
                "success": True,
                "message": f"Growth measurement added for {request.date}",
                "measurement": {"date": request.date, "height": request.height, "weight": request.weight},
                "percentiles": percentiles,
                "growth_status": self._assess_growth_status(percentiles)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def record_milestone(self, request: MilestoneRequest) -> Dict[str, Any]:
        """Milestone recording with development assessment"""
        try:
            result = self.tracker.record_milestone(request.milestone, request.date)
            development_status = self.tracker.get_development_status()
            return {
                "success": True,
                "message": f"Milestone recorded: {request.milestone}",
                "milestone": {"milestone": request.milestone, "date": request.date},
                "development_status": development_status,
                "next_milestones": self._get_next_expected_milestones()
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def record_vaccination(self, request: VaccinationRequest) -> Dict[str, Any]:
        """Vaccination recording with schedule tracking"""
        try:
            result = self.tracker.record_vaccination(request.vaccine, request.date)
            vaccination_status = self.tracker.get_vaccination_status()
            return {
                "success": True,
                "message": f"Vaccination recorded: {request.vaccine}",
                "vaccination": {"vaccine": request.vaccine, "date": request.date},
                "vaccination_status": vaccination_status,
                "upcoming_vaccines": self._get_upcoming_vaccines()
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def analyze_growth(self, request: GrowthAnalysisRequest) -> Dict[str, Any]:
        """Growth analysis with comprehensive insights"""
        try:
            analysis = self.tracker.analyze_growth()
            percentiles = self.tracker.calculate_growth_percentiles()
            recommendations = self._generate_health_recommendations(analysis, percentiles) if request.include_recommendations else []
            
            return {
                "success": True,
                "analysis": analysis,
                "percentiles": percentiles,
                "growth_trends": self._calculate_growth_trends(),
                "health_recommendations": recommendations,
                "risk_factors": self._identify_risk_factors(analysis, percentiles),
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def predict_growth(self, request: GrowthPredictionRequest) -> Dict[str, Any]:
        """Growth prediction with confidence intervals"""
        try:
            prediction = self.tracker.predict_growth(request.target_age_months)
            return {
                "success": True,
                "target_age_months": request.target_age_months,
                "prediction": prediction,
                "confidence_level": self._calculate_prediction_confidence(prediction),
                "factors_considered": ["current_growth_rate", "genetic_factors", "nutrition_status", "health_history"]
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_photo_memory(self, request: PhotoMemoryRequest) -> Dict[str, Any]:
        """Photo memory with metadata extraction"""
        try:
            result = self.tracker.add_photo_memory(request.image_base64, request.caption, request.date)
            return {
                "success": True,
                "message": f"Photo memory added for {request.date}",
                "photo_info": {"caption": request.caption, "date": request.date, "size_kb": len(request.image_base64) // 1024},
                "total_memories": len(self.tracker.child_data.get("photo_memories", []))
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_doctor_note(self, request: DoctorNoteRequest) -> Dict[str, Any]:
        """Doctor note with medical insights"""
        try:
            result = self.tracker.add_doctor_note(request.date, request.note, request.doctor)
            return {
                "success": True,
                "message": f"Doctor note added for {request.date}",
                "note_info": {"date": request.date, "doctor": request.doctor, "note_length": len(request.note)},
                "medical_keywords": self._extract_medical_keywords(request.note),
                "total_notes": len(self.tracker.child_data.get("doctor_notes", []))
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_reminder(self, request: ReminderRequest) -> Dict[str, Any]:
        """Reminder with smart scheduling"""
        try:
            result = self.tracker.add_reminder(request.reminder_type, request.due_date, request.notes)
            return {
                "success": True,
                "message": f"Reminder added: {request.reminder_type}",
                "reminder_info": {"type": request.reminder_type, "due_date": request.due_date, "notes": request.notes},
                "days_until_due": self._calculate_days_until_due(request.due_date),
                "priority": self._calculate_reminder_priority(request.reminder_type, request.due_date)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def generate_full_report(self, request: FullReportRequest) -> Dict[str, Any]:
        """Comprehensive report generation"""
        try:
            report = self.tracker.generate_comprehensive_report(include_chart=request.include_chart)
            
            # Enhanced report with additional insights
            enhanced_report = {
                **report,
                "report_summary": self._generate_report_summary(report),
                "key_insights": self._extract_key_insights(report),
                "action_items": self._generate_action_items(report),
                "generated_at": datetime.now().isoformat(),
                "report_completeness": self._assess_report_completeness(report)
            }
            
            return {"success": True, "comprehensive_report": enhanced_report}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    # --- Elite Helper Methods ---
    def _assess_growth_status(self, percentiles: Dict[str, Any]) -> str:
        """Growth status assessment"""
        height_percentile = percentiles.get("height_percentile", 50)
        weight_percentile = percentiles.get("weight_percentile", 50)
        
        if height_percentile < 5 or weight_percentile < 5:
            return "Below normal range - consult pediatrician"
        elif height_percentile > 95 or weight_percentile > 95:
            return "Above normal range - monitor closely"
        elif 25 <= height_percentile <= 75 and 25 <= weight_percentile <= 75:
            return "Normal growth pattern"
        else:
            return "Within normal range - continue monitoring"
    
    def _get_next_expected_milestones(self) -> List[str]:
        """Milestone prediction"""
        current_age_months = self.tracker.get_current_age_months()
        milestone_map = {
            6: ["Sits without support", "Begins eating solid foods"],
            12: ["First words", "Walks independently"],
            18: ["Vocabulary of 10-20 words", "Climbs stairs"],
            24: ["Two-word phrases", "Potty training readiness"],
            36: ["Complete sentences", "Pedals tricycle"]
        }
        
        for age, milestones in milestone_map.items():
            if current_age_months < age:
                return milestones
        return ["Consult pediatrician for age-appropriate milestones"]
    
    def _get_upcoming_vaccines(self) -> List[str]:
        """Vaccination scheduling"""
        current_age_months = self.tracker.get_current_age_months()
        vaccine_schedule = {
            2: ["DTaP", "IPV", "Hib", "PCV13", "RV"],
            4: ["DTaP", "IPV", "Hib", "PCV13", "RV"],
            6: ["DTaP", "IPV", "Hib", "PCV13", "RV"],
            12: ["MMR", "Varicella", "Hep A"],
            15: ["DTaP", "Hib", "PCV13"],
            18: ["Hep A"],
            48: ["DTaP", "IPV", "MMR", "Varicella"]
        }
        
        for age, vaccines in vaccine_schedule.items():
            if current_age_months < age:
                return vaccines
        return ["Consult pediatrician for vaccination schedule"]
    
    def _generate_health_recommendations(self, analysis: Dict[str, Any], percentiles: Dict[str, Any]) -> List[str]:
        """Health recommendations"""
        recommendations = []
        
        height_percentile = percentiles.get("height_percentile", 50)
        weight_percentile = percentiles.get("weight_percentile", 50)
        
        if weight_percentile < 10:
            recommendations.append("Consider nutritional assessment and dietary consultation")
        elif weight_percentile > 90:
            recommendations.append("Monitor caloric intake and increase physical activity")
        
        if height_percentile < 10:
            recommendations.append("Evaluate for growth hormone deficiency")
        
        recommendations.extend([
            "Maintain regular pediatric checkups",
            "Ensure balanced nutrition with adequate protein and calcium",
            "Encourage age-appropriate physical activities",
            "Monitor developmental milestones consistently"
        ])
        
        return recommendations[:5]
    
    def _calculate_growth_trends(self) -> Dict[str, Any]:
        """Growth trend analysis"""
        measurements = self.tracker.child_data.get("growth_measurements", [])
        if len(measurements) < 2:
            return {"trend": "insufficient_data", "message": "Need more measurements for trend analysis"}
        
        recent_measurements = sorted(measurements, key=lambda x: x["date"])[-3:]
        height_trend = "stable"
        weight_trend = "stable"
        
        if len(recent_measurements) >= 2:
            height_change = recent_measurements[-1]["height"] - recent_measurements[0]["height"]
            weight_change = recent_measurements[-1]["weight"] - recent_measurements[0]["weight"]
            
            height_trend = "increasing" if height_change > 2 else "decreasing" if height_change < -1 else "stable"
            weight_trend = "increasing" if weight_change > 0.5 else "decreasing" if weight_change < -0.5 else "stable"
        
        return {
            "height_trend": height_trend,
            "weight_trend": weight_trend,
            "measurements_analyzed": len(recent_measurements),
            "trend_period_days": (datetime.strptime(recent_measurements[-1]["date"], "%Y-%m-%d") - 
                                datetime.strptime(recent_measurements[0]["date"], "%Y-%m-%d")).days if len(recent_measurements) >= 2 else 0
        }
    
    def _identify_risk_factors(self, analysis: Dict[str, Any], percentiles: Dict[str, Any]) -> List[str]:
        """Risk factor identification"""
        risk_factors = []
        
        if percentiles.get("height_percentile", 50) < 5:
            risk_factors.append("Growth retardation risk")
        if percentiles.get("weight_percentile", 50) < 5:
            risk_factors.append("Underweight/malnutrition risk")
        if percentiles.get("weight_percentile", 50) > 95:
            risk_factors.append("Childhood obesity risk")
        
        return risk_factors
    
    def _calculate_prediction_confidence(self, prediction: Dict[str, Any]) -> str:
        """Prediction confidence calculation"""
        measurements_count = len(self.tracker.child_data.get("growth_measurements", []))
        
        if measurements_count >= 6:
            return "High confidence"
        elif measurements_count >= 3:
            return "Medium confidence"
        else:
            return "Low confidence - more measurements needed"
    
    def _extract_medical_keywords(self, note: str) -> List[str]:
        """Medical keyword extraction"""
        medical_keywords = ["fever", "cough", "rash", "infection", "allergy", "medication", "treatment", "diagnosis", "symptoms", "examination"]
        found_keywords = [keyword for keyword in medical_keywords if keyword.lower() in note.lower()]
        return found_keywords[:5]
    
    def _calculate_days_until_due(self, due_date: str) -> int:
        """Due date calculation"""
        try:
            due = datetime.strptime(due_date, "%Y-%m-%d")
            today = datetime.now()
            return (due - today).days
        except:
            return 0
    
    def _calculate_reminder_priority(self, reminder_type: str, due_date: str) -> str:
        """Priority calculation"""
        days_until = self._calculate_days_until_due(due_date)
        
        if "vaccination" in reminder_type.lower() or "appointment" in reminder_type.lower():
            return "High" if days_until <= 7 else "Medium"
        elif days_until <= 3:
            return "High"
        elif days_until <= 14:
            return "Medium"
        else:
            return "Low"
    
    def _generate_report_summary(self, report: Dict[str, Any]) -> str:
        """Report summary generation"""
        child_name = report.get("child_profile", {}).get("name", "Child")
        measurements_count = len(report.get("growth_measurements", []))
        milestones_count = len(report.get("milestones", []))
        
        return f"{child_name}'s comprehensive health report with {measurements_count} growth measurements and {milestones_count} developmental milestones recorded."
    
    def _extract_key_insights(self, report: Dict[str, Any]) -> List[str]:
        """Insight extraction"""
        insights = []
        
        if report.get("growth_analysis"):
            insights.append("Growth patterns analyzed and documented")
        if report.get("milestones"):
            insights.append(f"{len(report['milestones'])} developmental milestones achieved")
        if report.get("vaccinations"):
            insights.append(f"Vaccination record maintained with {len(report['vaccinations'])} vaccines")
        
        return insights[:3]
    
    def _generate_action_items(self, report: Dict[str, Any]) -> List[str]:
        """Action item generation"""
        actions = []
        
        if len(report.get("growth_measurements", [])) < 3:
            actions.append("Schedule regular growth measurements")
        if not report.get("vaccinations"):
            actions.append("Update vaccination records")
        if len(report.get("doctor_notes", [])) == 0:
            actions.append("Schedule pediatric consultation")
        
        return actions[:3]
    
    def _assess_report_completeness(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """Completeness assessment"""
        sections = ["child_profile", "growth_measurements", "milestones", "vaccinations"]
        completed_sections = sum(1 for section in sections if report.get(section))
        completeness_percentage = (completed_sections / len(sections)) * 100
        
        return {
            "completeness_percentage": completeness_percentage,
            "completed_sections": completed_sections,
            "total_sections": len(sections),
            "status": "Complete" if completeness_percentage >= 75 else "Partial"
        }

# Create tool instance
child_growth_tracker_tool = ChildGrowthTrackerTool()
