from langchain.tools import Tool
import json
from datetime import datetime
import logging

class HealthScoreAnalysisTool:
    """
    A comprehensive tool that combines vitals, lifestyle, and test results into a dynamic health score.
    Provides holistic health awareness through a weighted scoring engine.
    """

    def __init__(self):
        # Define base scoring criteria for vital signs and tests
        self.scoring_criteria = {
            "Glucose": {"range": (70, 100), "unit": "mg/dL"},
            "SpO2": {"range": (95, 100), "unit": "%"},
            "Blood Pressure (Systolic)": {"range": (90, 120), "unit": "mmHg"},
            "Blood Pressure (Diastolic)": {"range": (60, 80), "unit": "mmHg"},
            "Weight (BMI)": {"range": (18.5, 24.9), "unit": "kg/m²"},
            "Temperature": {"range": (36.5, 37.5), "unit": "°C"},
            "ECG (Heart Rate)": {"range": (60, 100), "unit": "BPM"},
            "Malaria": {"range": "Negative", "unit": "Binary"},
            "Widal Test": {"range": "Negative", "unit": "Binary"},
            "Hepatitis B": {"range": "Negative", "unit": "Binary"},
            "Voluntary Serology": {"range": "Negative", "unit": "Binary"},
            "Waist Circumference": {"range": (80, 94), "unit": "cm"},
            "Respiratory Rate": {"range": (12, 20), "unit": "breaths/min"},
            "Fev": {"range": (80, 120), "unit": "%"},
        }

        # Define weights for different health categories - evidence-based weighting
        self.category_weights = {
            "vitals": 0.40,           # 40% weight for vital signs (critical physiological indicators)
            "lifestyle": 0.35,        # 35% weight for lifestyle factors (preventive health determinants)
            "test_results": 0.25      # 25% weight for test results (diagnostic indicators)
        }

        # Define vital signs parameters and their weights
        self.vital_weights = {
            "Blood Pressure (Systolic)": 0.12,
            "Blood Pressure (Diastolic)": 0.12,
            "ECG (Heart Rate)": 0.12,
            "SpO2": 0.12,
            "Temperature": 0.08,
            "Weight (BMI)": 0.12,
            "Waist Circumference": 0.08,
            "Fev": 0.10,
            "Respiratory Rate": 0.08
        }

        # Define lifestyle parameters and their weights
        self.lifestyle_weights = {
            "Exercise": 0.20,
            "Diet": 0.20,
            "Sleep": 0.15,
            "Stress": 0.12,
            "Hydration": 0.08,
            "Smoking": 0.10,
            "Alcohol": 0.08,
            "Social Connection": 0.07
        }

        # Define test results parameters and their weights
        self.test_weights = {
            "Glucose": 0.12,
            "Malaria": 0.05,
            "Widal Test": 0.03,
            "Hepatitis B": 0.05,
            "Voluntary Serology": 0.03,
            "Kidney Function": 0.18,
            "Lipid Profile": 0.18,
            "Liver Function": 0.12,
            "Complete Blood Count": 0.12
        }

        # Define normal ranges for lifestyle factors
        self.lifestyle_ranges = {
            "Exercise": {"range": (3, 5), "unit": "days/week"},
            "Diet": {"range": (3, 5), "unit": "score"},
            "Sleep": {"range": (7, 9), "unit": "hours"},
            "Stress": {"range": (1, 2), "unit": "score"},
            "Hydration": {"range": (8, 12), "unit": "cups"},
            "Smoking": {"range": (0, 0), "unit": "cigarettes"},
            "Alcohol": {"range": (0, 2), "unit": "drinks/day"},
            "Social Connection": {"range": (3, 5), "unit": "score"}
        }

        # Add ranges for integrated test results
        self.test_ranges = {
            "Kidney Function": {"range": "Good", "unit": "Status"},
            "Lipid Profile": {"range": "Normal", "unit": "Status"},
            "Liver Function": {"range": "Normal", "unit": "Status"},
            "Complete Blood Count": {"range": "Normal", "unit": "Status"}
        }

        # Define trend thresholds for significant changes
        self.trend_thresholds = {
            "score_change": 5,  # 5% change in overall score is significant
            "vital_change": 10,  # 10% change in vital parameter is significant
            "lifestyle_change": 15,  # 15% change in lifestyle factor is significant
            "test_change": 10  # 10% change in test result is significant
        }

    def evaluate_health_metric(self, metric, value):
        """Evaluates a health metric against its normal range."""
        # Check in scoring criteria first
        if metric in self.scoring_criteria:
            criteria = self.scoring_criteria[metric]
            expected_range = criteria["range"]

            if isinstance(expected_range, tuple):  # Numerical value check
                if not isinstance(value, (int, float)):
                    return "Invalid data"
                low, high = expected_range
                if low <= value <= high:
                    return "Normal"
                elif value < low:
                    return "Low"
                else:
                    return "High"

            elif isinstance(expected_range, str):  # Binary test check
                if value == "Unknown":
                    return "Unknown"  # Treat Unknown as null value
                elif value == expected_range:
                    return "Negative"
                else:
                    return "Positive"

        # Check in lifestyle ranges
        elif metric in self.lifestyle_ranges:
            criteria = self.lifestyle_ranges[metric]
            expected_range = criteria["range"]

            if isinstance(expected_range, tuple):
                if not isinstance(value, (int, float)):
                    return "Invalid data"
                low, high = expected_range
                if low <= value <= high:
                    return "Normal"
                elif value < low:
                    return "Low"
                else:
                    return "High"

        # Check in test ranges
        elif metric in self.test_ranges:
            criteria = self.test_ranges[metric]
            expected_range = criteria["range"]

            if isinstance(expected_range, str):
                if isinstance(value, str):
                    if value.lower() == expected_range.lower() or "normal" in value.lower() or "good" in value.lower():
                        return "Normal"
                    else:
                        return "Abnormal"
                return "Unknown"

        return "Unknown"

    def calculate_vital_score(self, health_data):
        """Calculate score for vital signs using weighted scoring"""
        vital_score = 0
        max_vital_score = 0
        vital_issues = []

        for vital, weight in self.vital_weights.items():
            if vital in health_data and health_data[vital] not in [None, "", "null"]:
                max_vital_score += weight
                value = health_data[vital]

                # Evaluate the vital sign
                status = self.evaluate_health_metric(vital, value)

                if status == "Normal" or status == "Negative":
                    vital_score += weight
                elif status in ["Low", "High", "Abnormal", "Positive"]:
                    vital_issues.append(f"{vital} ({status})")

        # Normalize score if we have data
        normalized_score = (vital_score / max_vital_score) * 100 if max_vital_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": vital_issues
        }

    def calculate_lifestyle_score(self, health_data):
        """Calculate score for lifestyle factors using weighted scoring"""
        lifestyle_score = 0
        max_lifestyle_score = 0
        lifestyle_issues = []

        for factor, weight in self.lifestyle_weights.items():
            if factor in health_data and health_data[factor] not in [None, "", "null"]:
                max_lifestyle_score += weight
                value = health_data[factor]

                # Evaluate the lifestyle factor
                status = self.evaluate_health_metric(factor, value)

                if status == "Normal":
                    lifestyle_score += weight
                else:
                    lifestyle_issues.append(f"{factor} ({status})")

        # Normalize score if we have data
        normalized_score = (lifestyle_score / max_lifestyle_score) * 100 if max_lifestyle_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": lifestyle_issues
        }

    def calculate_test_score(self, health_data):
        """Calculate score for test results using weighted scoring"""
        test_score = 0
        max_test_score = 0
        test_issues = []

        for test, weight in self.test_weights.items():
            if test in health_data and health_data[test] not in [None, "", "null"]:
                max_test_score += weight
                value = health_data[test]

                # Handle integrated test results
                if test in ["Kidney Function", "Lipid Profile", "Liver Function", "Complete Blood Count"]:
                    if isinstance(value, str):
                        if value == "Unknown":
                            # Skip Unknown values - don't count them as issues
                            pass
                        elif "good" in value.lower() or "normal" in value.lower() or "excellent" in value.lower():
                            test_score += weight
                        else:
                            test_issues.append(f"{test} ({value})")
                # Handle standard test results
                else:
                    status = self.evaluate_health_metric(test, value)

                    if status == "Normal" or status == "Negative":
                        test_score += weight
                    elif status == "Unknown":
                        # Skip Unknown values - don't count them as issues
                        pass
                    elif status in ["Low", "High", "Abnormal", "Positive"]:
                        test_issues.append(f"{test} ({status})")

        # Normalize score if we have data
        normalized_score = (test_score / max_test_score) * 100 if max_test_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": test_issues
        }

    def generate_improvement_tips(self, vital_issues, lifestyle_issues, test_issues):
        """Generate personalized improvement tips based on identified issues"""
        tips = []

        # Track categories to avoid repetitive tips
        tip_categories = {
            "doctor_visit": False,
            "breathing": False,
            "diet": False,
            "exercise": False,
            "blood_pressure": False,
            "sleep": False,
            "stress": False,
            "hydration": False
        }

        # Generate tips for vital issues
        for issue in vital_issues:
            if "Blood Pressure" in issue:
                if not tip_categories["blood_pressure"]:
                    tips.append("🩸 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.")
                    tip_categories["blood_pressure"] = True
            elif "SpO2" in issue:
                if not tip_categories["breathing"]:
                    tips.append("🫁 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.")
                    tip_categories["breathing"] = True
            elif "Weight (BMI)" in issue:
                if "High" in issue and not tip_categories["diet"] and not tip_categories["exercise"]:
                    tips.append("💪 Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.")
                    tip_categories["diet"] = True
                    tip_categories["exercise"] = True
                elif "Low" in issue and not tip_categories["diet"]:
                    tips.append("🥗 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight.")
                    tip_categories["diet"] = True
            elif "Glucose" in issue:
                if not tip_categories["diet"]:
                    tips.append("🍎 To help manage your glucose levels, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.")
                    tip_categories["diet"] = True

        # Generate tips for lifestyle issues
        for issue in lifestyle_issues:
            if "Exercise" in issue:
                if not tip_categories["exercise"]:
                    tips.append("🏃‍♀️ Try to incorporate at least 150 minutes of moderate activity per week, broken into sessions as short as 10 minutes if needed.")
                    tip_categories["exercise"] = True
            elif "Diet" in issue:
                if not tip_categories["diet"]:
                    tips.append("🥦 Focus on a balanced diet with plenty of vegetables, fruits, whole grains, lean proteins, and healthy fats while limiting processed foods.")
                    tip_categories["diet"] = True
            elif "Sleep" in issue:
                if not tip_categories["sleep"]:
                    tips.append("😴 Improve your sleep by maintaining a consistent schedule, creating a relaxing bedtime routine, and keeping your bedroom cool, dark, and quiet.")
                    tip_categories["sleep"] = True
            elif "Stress" in issue:
                if not tip_categories["stress"]:
                    tips.append("🧘‍♀️ Practice stress management techniques like mindfulness meditation, deep breathing, or progressive muscle relaxation for at least 10 minutes daily.")
                    tip_categories["stress"] = True
            elif "Hydration" in issue:
                if not tip_categories["hydration"]:
                    tips.append("💧 Improve hydration by carrying a reusable water bottle, setting reminders to drink throughout the day, and eating water-rich foods like cucumbers and watermelon.")
                    tip_categories["hydration"] = True
            elif "Smoking" in issue:
                tips.append("🚭 Consider talking to your healthcare provider about smoking cessation programs, nicotine replacement therapy, or medications that can help you quit.")

        # Generate tips for test issues
        for issue in test_issues:
            if any(test in issue for test in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]):
                if not tip_categories["doctor_visit"]:
                    tips.append("🩺 Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.")
                    tip_categories["doctor_visit"] = True
            elif "Kidney Function" in issue:
                tips.append("🧪 To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.")
            elif "Lipid Profile" in issue:
                tips.append("❤️ To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables.")

        # If no tips were generated but there are issues
        if not tips and (vital_issues or lifestyle_issues or test_issues):
            tips.append("🌟 Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.")

        # If everything is normal
        if not tips and not vital_issues and not lifestyle_issues and not test_issues:
            tips.append("🌟 Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!")

        return tips

    def generate_report(self, health_data: dict) -> dict:
        """
        Generate a comprehensive health report using weighted scoring across multiple health categories.
        This is the main method that maintains backward compatibility with existing code.
        """
        try:
            # Extract the actual health data if it's in a nested format
            if isinstance(health_data, dict) and "data" in health_data:
                health_data = health_data["data"]

            # Calculate scores for each category
            vital_results = self.calculate_vital_score(health_data)
            lifestyle_results = self.calculate_lifestyle_score(health_data)
            test_results = self.calculate_test_score(health_data)

            # Calculate weighted total score
            categories_with_data = 0
            weighted_score = 0

            if vital_results["score"] > 0:
                weighted_score += vital_results["score"] * self.category_weights["vitals"]
                categories_with_data += 1

            if lifestyle_results["score"] > 0:
                weighted_score += lifestyle_results["score"] * self.category_weights["lifestyle"]
                categories_with_data += 1

            if test_results["score"] > 0:
                weighted_score += test_results["score"] * self.category_weights["test_results"]
                categories_with_data += 1

            # Normalize the weighted score based on available data
            if categories_with_data > 0:
                # Adjust weights based on available data
                adjusted_weight_sum = 0
                if vital_results["score"] > 0:
                    adjusted_weight_sum += self.category_weights["vitals"]
                if lifestyle_results["score"] > 0:
                    adjusted_weight_sum += self.category_weights["lifestyle"]
                if test_results["score"] > 0:
                    adjusted_weight_sum += self.category_weights["test_results"]

                # Calculate final score
                final_score = round(weighted_score / adjusted_weight_sum) if adjusted_weight_sum > 0 else 0
            else:
                # Fall back to legacy scoring if no category data is available
                total_score = 0
                max_score = 0

                for key, value in health_data.items():
                    if value in [None, '', 'null']:
                        continue  # Skip missing or null fields

                    status = self.evaluate_health_metric(key, value)
                    if key in self.scoring_criteria:
                        max_score += 5
                        if status == "Normal" or status == "Negative":
                            total_score += 5

                final_score = round((total_score / max_score) * 100) if max_score > 0 else 0

            # Determine health status
            if final_score >= 85:
                status = "Excellent"
            elif final_score >= 70:
                status = "Good"
            elif final_score >= 50:
                status = "Fair"
            else:
                status = "Poor"

            # Generate improvement tips
            improvement_tips = self.generate_improvement_tips(
                vital_results["issues"],
                lifestyle_results["issues"],
                test_results["issues"]
            )

            # Add device recommendation prompt (following agent_app pattern)
            device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your health score results? (Yes/No)**"

            # Create the final result - maintain backward compatibility
            result = {
                "Total Score": final_score,
                "Health Status": status,
                "Vitals Needing Improvement": ", ".join(vital_results["issues"]) if vital_results["issues"] else "None",
                "Improvement Tips": " ".join(improvement_tips) if improvement_tips else "Keep maintaining your health!",
            }

            # Add enhanced data for new clients that can use it
            result.update({
                "timestamp": datetime.now().isoformat(),
                "Category Scores": {
                    "Vitals": round(vital_results["score"]),
                    "Lifestyle": round(lifestyle_results["score"]),
                    "Test Results": round(test_results["score"])
                },
                "Detailed Issues": {
                    "Vitals": vital_results["issues"],
                    "Lifestyle": lifestyle_results["issues"],
                    "Test Results": test_results["issues"]
                },
                "Detailed Improvement Tips": improvement_tips,
                "device_recommendation_prompt": device_recommendation_prompt,
                "test_type": "realtime_health_score"
            })

            return result

        except Exception as e:
            logging.error(f"Error generating health report: {str(e)}")
            return {
                "Total Score": 0,
                "Health Status": "Unknown",
                "Vitals Needing Improvement": "Error in analysis",
                "Improvement Tips": "Please try again or consult a healthcare professional.",
                "error": f"Failed to analyze health data: {str(e)}"
            }

    def analyze_health_trends(self, current_data, historical_data):
        """
        Analyze trends in health data over time to identify improvements or deteriorations.

        Args:
            current_data: The most recent health data
            historical_data: List of previous health data points in chronological order

        Returns:
            Dictionary with trend analysis results
        """
        if not historical_data:
            return {"message": "No historical data available for trend analysis"}

        trends = {
            "improving": [],
            "deteriorating": [],
            "stable": [],
            "significant_changes": []
        }

        # Get the most recent historical data point for comparison
        previous_data = historical_data[-1]

        # Compare vital signs
        for vital in self.vital_weights.keys():
            if vital in current_data and vital in previous_data:
                current_value = current_data[vital]
                previous_value = previous_data[vital]

                if isinstance(current_value, (int, float)) and isinstance(previous_value, (int, float)):
                    percent_change = ((current_value - previous_value) / previous_value) * 100 if previous_value != 0 else 0

                    if abs(percent_change) >= self.trend_thresholds["vital_change"]:
                        trends["significant_changes"].append({
                            "parameter": vital,
                            "previous": previous_value,
                            "current": current_value,
                            "percent_change": round(percent_change, 1)
                        })

                    if self.evaluate_health_metric(vital, current_value) == "Normal" and self.evaluate_health_metric(vital, previous_value) != "Normal":
                        trends["improving"].append(vital)
                    elif self.evaluate_health_metric(vital, current_value) != "Normal" and self.evaluate_health_metric(vital, previous_value) == "Normal":
                        trends["deteriorating"].append(vital)
                    else:
                        trends["stable"].append(vital)

        # Compare overall scores if available
        if "Total Score" in current_data and "Total Score" in previous_data:
            current_score = current_data["Total Score"]
            previous_score = previous_data["Total Score"]

            score_change = current_score - previous_score

            if abs(score_change) >= self.trend_thresholds["score_change"]:
                trends["significant_changes"].append({
                    "parameter": "Total Health Score",
                    "previous": previous_score,
                    "current": current_score,
                    "change": score_change
                })

            if score_change > 0:
                trends["overall"] = "Improving"
            elif score_change < 0:
                trends["overall"] = "Deteriorating"
            else:
                trends["overall"] = "Stable"

        return trends


# Create a Tool instance for use with LangChain
health_score_analysis_tool = Tool(
    name="HealthScoreAnalysis",
    func=lambda x: json.dumps(HealthScoreAnalysisTool().generate_report(json.loads(x)), indent=2),
    description="Analyzes comprehensive health data including vitals, lifestyle factors, and test results to generate a holistic health score with personalized recommendations."
)
