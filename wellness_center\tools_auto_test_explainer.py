"""Auto Test Result Explainer - Ultra-Optimized QA Tool"""

import json, statistics, re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from pydantic import BaseModel, Field

# --- Elite Models ---
class TestResult(BaseModel):
    test_name: str
    test_value: Any
    reference_range: Optional[str] = None
    units: Optional[str] = None

class PatientDemographics(BaseModel):
    patient_id: str
    age: int
    sex: str

class AutoTestExplainerRequest(BaseModel):
    technician_id: str
    patient_demographics: PatientDemographics
    test_results: List[TestResult]

class QualityFlag(BaseModel):
    flag_type: str
    severity: str
    test_name: str
    description: str
    recommendation: str

# --- Elite Tool Class ---
class AutoTestExplainerTool:
    """Auto test explainer with minimal code, maximum QA functionality"""
    
    def __init__(self):
        self.name = "auto_test_explainer"
        # Elite reference ranges with pattern matching
        self.reference_ranges = {
            "glucose": {"normal": (70, 100), "prediabetic": (100, 125), "diabetic": (126, float('inf'))},
            "cholesterol": {"normal": (0, 200), "borderline": (200, 239), "high": (240, float('inf'))},
            "blood_pressure_systolic": {"normal": (90, 120), "elevated": (120, 129), "high": (130, float('inf'))},
            "blood_pressure_diastolic": {"normal": (60, 80), "elevated": (80, 89), "high": (90, float('inf'))},
            "heart_rate": {"low": (0, 60), "normal": (60, 100), "high": (100, float('inf'))},
            "temperature": {"low": (0, 97), "normal": (97, 99.5), "high": (99.5, float('inf'))},
            "hemoglobin": {"low_male": (0, 13.8), "normal_male": (13.8, 17.2), "high_male": (17.2, float('inf')), "low_female": (0, 12.1), "normal_female": (12.1, 15.1), "high_female": (15.1, float('inf'))},
            "white_blood_cells": {"low": (0, 4.5), "normal": (4.5, 11.0), "high": (11.0, float('inf'))},
            "platelets": {"low": (0, 150), "normal": (150, 450), "high": (450, float('inf'))}
        }
        
        # Elite age-based adjustments
        self.age_adjustments = {
            "pediatric": (0, 18), "adult": (18, 65), "elderly": (65, float('inf'))
        }
    
    def analyze_test_results(self, request: AutoTestExplainerRequest) -> Dict[str, Any]:
        """Test analysis with functional approach"""
        try:
            flags = self._detect_quality_flags(request.test_results, request.patient_demographics)
            confidence_score = self._calculate_confidence_score(flags, request.test_results)
            outliers = self._detect_outliers(request.test_results, request.patient_demographics)
            consistency_check = self._check_data_consistency(request.test_results)
            recommendations = self._generate_recommendations(flags, outliers, consistency_check)
            
            return {
                "success": True,
                "technician_id": request.technician_id,
                "patient_id": request.patient_demographics.patient_id,
                "analysis_timestamp": datetime.now().isoformat(),
                "confidence_score": confidence_score,
                "quality_flags": [flag.dict() for flag in flags],
                "outlier_analysis": outliers,
                "consistency_check": consistency_check,
                "recommendations": recommendations,
                "overall_assessment": self._generate_overall_assessment(confidence_score, flags),
                "total_tests_analyzed": len(request.test_results),
                "flags_detected": len(flags)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _detect_quality_flags(self, test_results: List[TestResult], demographics: PatientDemographics) -> List[QualityFlag]:
        """Flag detection with pattern matching"""
        flags = []
        
        for test in test_results:
            test_name_lower = test.test_name.lower()
            
            # Missing data flags
            if test.test_value in [None, "", "N/A", "Unknown"]:
                flags.append(QualityFlag(
                    flag_type="missing_data",
                    severity="medium",
                    test_name=test.test_name,
                    description=f"Missing or invalid test value for {test.test_name}",
                    recommendation="Verify test was completed and recorded correctly"
                ))
                continue
            
            # Numeric value analysis
            if isinstance(test.test_value, (int, float)):
                # Range-based flags
                for ref_test, ranges in self.reference_ranges.items():
                    if ref_test in test_name_lower:
                        flag = self._check_numeric_ranges(test, ranges, demographics)
                        if flag:
                            flags.append(flag)
                        break
                
                # Extreme value flags
                if test.test_value < 0:
                    flags.append(QualityFlag(
                        flag_type="technical_error",
                        severity="high",
                        test_name=test.test_name,
                        description="Negative value detected",
                        recommendation="Verify measurement and recalibrate equipment if necessary"
                    ))
                elif test.test_value > 10000:  # Arbitrary high threshold
                    flags.append(QualityFlag(
                        flag_type="outlier",
                        severity="medium",
                        test_name=test.test_name,
                        description="Extremely high value detected",
                        recommendation="Double-check measurement and patient preparation"
                    ))
        
        return flags
    
    def _check_numeric_ranges(self, test: TestResult, ranges: Dict[str, Tuple[float, float]], demographics: PatientDemographics) -> Optional[QualityFlag]:
        """Numeric range checking with demographic consideration"""
        value = float(test.test_value)
        
        # Gender-specific ranges
        if demographics.sex.lower() == "male" and "male" in ranges:
            range_key = "normal_male"
        elif demographics.sex.lower() == "female" and "female" in ranges:
            range_key = "normal_female"
        else:
            range_key = "normal"
        
        if range_key not in ranges:
            range_key = "normal"
        
        if range_key in ranges:
            min_val, max_val = ranges[range_key]
            if not (min_val <= value <= max_val):
                severity = "high" if value > max_val * 2 or value < min_val * 0.5 else "medium"
                return QualityFlag(
                    flag_type="outlier",
                    severity=severity,
                    test_name=test.test_name,
                    description=f"Value {value} outside normal range ({min_val}-{max_val})",
                    recommendation="Review patient history and consider retesting"
                )
        
        return None
    
    def _calculate_confidence_score(self, flags: List[QualityFlag], test_results: List[TestResult]) -> float:
        """Confidence scoring with weighted penalties"""
        if not test_results:
            return 0.0
        
        base_score = 100.0
        severity_penalties = {"low": 2, "medium": 5, "high": 10, "critical": 20}
        
        total_penalty = sum(severity_penalties.get(flag.severity, 5) for flag in flags)
        confidence_score = max(0.0, base_score - total_penalty)
        
        # Bonus for complete data
        complete_tests = sum(1 for test in test_results if test.test_value not in [None, "", "N/A", "Unknown"])
        completeness_bonus = (complete_tests / len(test_results)) * 10
        
        return min(100.0, confidence_score + completeness_bonus)
    
    def _detect_outliers(self, test_results: List[TestResult], demographics: PatientDemographics) -> Dict[str, Any]:
        """Elite outlier detection with statistical analysis"""
        numeric_values = [float(test.test_value) for test in test_results if isinstance(test.test_value, (int, float)) and test.test_value not in [None, "", "N/A"]]
        
        if len(numeric_values) < 2:
            return {"outliers_detected": 0, "statistical_analysis": "Insufficient numeric data"}
        
        mean_val = statistics.mean(numeric_values)
        std_dev = statistics.stdev(numeric_values) if len(numeric_values) > 1 else 0
        
        outliers = []
        for test in test_results:
            if isinstance(test.test_value, (int, float)):
                z_score = abs((test.test_value - mean_val) / std_dev) if std_dev > 0 else 0
                if z_score > 2:  # 2 standard deviations
                    outliers.append({
                        "test_name": test.test_name,
                        "value": test.test_value,
                        "z_score": round(z_score, 2),
                        "severity": "high" if z_score > 3 else "medium"
                    })
        
        return {
            "outliers_detected": len(outliers),
            "outlier_details": outliers,
            "statistical_summary": {
                "mean": round(mean_val, 2),
                "std_dev": round(std_dev, 2),
                "total_numeric_tests": len(numeric_values)
            }
        }
    
    def _check_data_consistency(self, test_results: List[TestResult]) -> Dict[str, Any]:
        """Consistency checking with pattern analysis"""
        consistency_issues = []
        
        # Check for duplicate test names
        test_names = [test.test_name for test in test_results]
        duplicates = [name for name in set(test_names) if test_names.count(name) > 1]
        
        if duplicates:
            consistency_issues.append({
                "issue_type": "duplicate_tests",
                "description": f"Duplicate test names found: {', '.join(duplicates)}",
                "severity": "medium"
            })
        
        # Check for missing units on numeric values
        missing_units = [test.test_name for test in test_results if isinstance(test.test_value, (int, float)) and not test.units]
        
        if missing_units:
            consistency_issues.append({
                "issue_type": "missing_units",
                "description": f"Numeric tests missing units: {', '.join(missing_units[:3])}{'...' if len(missing_units) > 3 else ''}",
                "severity": "low"
            })
        
        # Check for inconsistent formatting
        inconsistent_formats = []
        for test in test_results:
            if isinstance(test.test_value, str) and re.search(r'\d+\.\d{3,}', str(test.test_value)):
                inconsistent_formats.append(test.test_name)
        
        if inconsistent_formats:
            consistency_issues.append({
                "issue_type": "formatting_inconsistency",
                "description": f"Inconsistent decimal precision in: {', '.join(inconsistent_formats[:3])}",
                "severity": "low"
            })
        
        return {
            "consistency_score": max(0, 100 - len(consistency_issues) * 15),
            "issues_found": len(consistency_issues),
            "issue_details": consistency_issues
        }
    
    def _generate_recommendations(self, flags: List[QualityFlag], outliers: Dict[str, Any], consistency: Dict[str, Any]) -> List[str]:
        """Recommendation generation with priority sorting"""
        recommendations = []
        
        # Critical flags first
        critical_flags = [f for f in flags if f.severity == "critical"]
        if critical_flags:
            recommendations.append("URGENT: Address critical quality flags before proceeding")
        
        # High severity flags
        high_flags = [f for f in flags if f.severity == "high"]
        if high_flags:
            recommendations.append(f"Review {len(high_flags)} high-severity quality issues")
        
        # Outlier recommendations
        if outliers.get("outliers_detected", 0) > 0:
            recommendations.append(f"Investigate {outliers['outliers_detected']} statistical outliers")
        
        # Consistency recommendations
        if consistency.get("consistency_score", 100) < 80:
            recommendations.append("Improve data consistency and formatting standards")
        
        # General recommendations
        if len(flags) == 0:
            recommendations.append("All tests appear within normal parameters")
        else:
            recommendations.append("Review flagged items before final approval")
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _generate_overall_assessment(self, confidence_score: float, flags: List[QualityFlag]) -> str:
        """Assessment generation with confidence thresholds"""
        critical_flags = sum(1 for f in flags if f.severity == "critical")
        high_flags = sum(1 for f in flags if f.severity == "high")
        
        if critical_flags > 0:
            return "CRITICAL: Requires immediate attention before approval"
        elif confidence_score >= 90:
            return "EXCELLENT: High confidence, ready for review"
        elif confidence_score >= 75:
            return "GOOD: Minor issues detected, review recommended"
        elif confidence_score >= 60:
            return "FAIR: Multiple issues detected, careful review required"
        else:
            return "POOR: Significant quality concerns, retest recommended"

# Create tool instance
auto_test_explainer_tool = AutoTestExplainerTool()
