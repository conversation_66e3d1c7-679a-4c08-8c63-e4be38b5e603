"""
Test script for Outbreak Detector integration with Wellness Center
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_outbreak_detector_basic():
    """Test basic outbreak detector functionality"""
    
    try:
        # Import the core outbreak detector
        from outbreak_detector import SchoolOutbreakDetector, SymptomReport, AbsenceRecord
        print("✅ Successfully imported core outbreak detector components")
        
        # Create detector instance
        detector = SchoolOutbreakDetector()
        print("✅ Successfully created detector instance")
        
        # Test basic symptom report creation
        symptom_report = SymptomReport(
            student_id="student_001",
            date_reported="2023-12-01",
            symptoms=["fever", "cough"],
            grade_level="5th",
            classroom="5A",
            severity="mild",
            reporter="nurse"
        )
        print("✅ Successfully created symptom report")
        
        # Test basic absence record creation
        absence_record = AbsenceRecord(
            student_id="student_002",
            date="2023-12-01",
            reason="sick",
            reported_by="parent",
            grade_level="5th",
            classroom="5A"
        )
        print("✅ Successfully created absence record")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic test error: {e}")
        return False

def test_outbreak_detector_tool():
    """Test the outbreak detector tool wrapper"""
    
    try:
        # Import directly from the tool file
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "tools_outbreak_detector", 
            "wellness_center/tools_outbreak_detector.py"
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✅ Successfully imported outbreak detector tool module")
        
        # Test request classes
        symptom_request = module.SymptomReportRequest(
            student_id="student_001",
            date_reported="2023-12-01",
            symptoms=["fever", "cough"],
            grade_level="5th",
            classroom="5A",
            severity="mild",
            reporter="nurse"
        )
        print("✅ SymptomReportRequest created successfully")
        
        absence_request = module.AbsenceReportRequest(
            student_id="student_002",
            date="2023-12-01",
            reason="sick",
            reported_by="parent",
            grade_level="5th",
            classroom="5A"
        )
        print("✅ AbsenceReportRequest created successfully")
        
        analysis_request = module.OutbreakAnalysisRequest(
            school_id="test_school",
            date_range_days=7,
            alert_threshold=0.7,
            enable_contact_tracing=True
        )
        print("✅ OutbreakAnalysisRequest created successfully")
        
        # Test tool instance
        tool = module.OutbreakDetectorTool()
        print("✅ OutbreakDetectorTool created successfully")
        
        # Test tool methods
        symptom_result = tool.add_symptom_report(symptom_request)
        print(f"✅ Tool symptom report addition: {symptom_result['success']}")
        
        absence_result = tool.add_absence_record(absence_request)
        print(f"✅ Tool absence record addition: {absence_result['success']}")
        
        # Test surveillance summary
        summary_result = tool.get_surveillance_summary()
        print(f"✅ Tool surveillance summary: {summary_result['success']}")
        
        # Test outbreak analysis
        analysis_result = tool.analyze_outbreak_risk(analysis_request)
        print(f"✅ Tool outbreak analysis: {analysis_result['success']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_analysis():
    """Test comprehensive outbreak analysis"""
    
    try:
        # Import the tool module
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "tools_outbreak_detector", 
            "wellness_center/tools_outbreak_detector.py"
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Create comprehensive request with sample data
        comprehensive_request = module.ComprehensiveOutbreakRequest(
            school_id="test_school",
            date_range_days=7,
            symptom_reports=[
                module.SymptomReportRequest(
                    student_id="student_001",
                    date_reported="2023-12-01",
                    symptoms=["fever", "cough"],
                    grade_level="5th",
                    classroom="5A"
                ),
                module.SymptomReportRequest(
                    student_id="student_002",
                    date_reported="2023-12-02",
                    symptoms=["fever", "sore throat"],
                    grade_level="5th",
                    classroom="5A"
                )
            ],
            absence_records=[
                module.AbsenceReportRequest(
                    student_id="student_003",
                    date="2023-12-01",
                    reason="sick",
                    grade_level="5th",
                    classroom="5A"
                ),
                module.AbsenceReportRequest(
                    student_id="student_004",
                    date="2023-12-02",
                    reason="sick",
                    grade_level="5th",
                    classroom="5B"
                )
            ],
            alert_threshold=0.7,
            enable_contact_tracing=True
        )
        
        # Test comprehensive analysis
        tool = module.OutbreakDetectorTool()
        result = tool.comprehensive_outbreak_analysis(comprehensive_request)
        
        print(f"✅ Comprehensive analysis: {result['success']}")
        
        if result['success']:
            analysis = result['comprehensive_analysis']
            print(f"   - Alert Level: {analysis.get('alert_level', 'N/A')}")
            print(f"   - Potential Outbreaks: {len(analysis.get('potential_outbreaks', []))}")
            print(f"   - High Risk Areas: {len(analysis.get('high_risk_areas', []))}")
            print(f"   - Recommendations: {len(analysis.get('recommended_actions', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive analysis test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Outbreak Detector Integration\n")
    
    print("1. Testing Basic Outbreak Detector...")
    basic_test = test_outbreak_detector_basic()
    
    print("\n2. Testing Outbreak Detector Tool...")
    tool_test = test_outbreak_detector_tool()
    
    print("\n3. Testing Comprehensive Analysis...")
    comprehensive_test = test_comprehensive_analysis()
    
    print(f"\n📊 Test Results:")
    print(f"   - Basic Functionality: {'✅ PASS' if basic_test else '❌ FAIL'}")
    print(f"   - Tool Integration: {'✅ PASS' if tool_test else '❌ FAIL'}")
    print(f"   - Comprehensive Analysis: {'✅ PASS' if comprehensive_test else '❌ FAIL'}")
    
    if basic_test and tool_test and comprehensive_test:
        print("\n🎉 All outbreak detector integration tests passed!")
        print("\nAvailable Outbreak Detector Endpoints:")
        print("   - POST /wellness/outbreak/add-symptom-report")
        print("   - POST /wellness/outbreak/add-absence-record")
        print("   - POST /wellness/outbreak/analyze-risk")
        print("   - POST /wellness/outbreak/comprehensive-analysis")
        print("   - GET  /wellness/outbreak/surveillance-summary")
        print("   - DELETE /wellness/outbreak/clear-data")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
