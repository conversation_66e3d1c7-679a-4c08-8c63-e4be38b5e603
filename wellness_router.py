# wellness_center/wellness_router.py

from fastapi import APIRouter
from typing import Dict, Any, List, Union
from pydantic import BaseModel

# --- Import AI Tools ---
from wellness_center.tools_unified_lab_manager import (
    UnifiedLabManagerTool,
    UnifiedLabRequest
)
from wellness_center.tools_auto_test_explainer import (
    auto_test_explainer_tool,
    AutoTestExplainerRequest
)
from wellness_center.tools_child_growth_tracker import (
    ChildGrowthTrackerTool,
    ChildProfileRequest,
    GrowthMeasurementRequest,
    MilestoneRequest,
    VaccinationRequest,
    GrowthAnalysisRequest,
    GrowthPredictionRequest,
    PhotoMemoryRequest,
    DoctorNoteRequest,
    ReminderRequest,
    FullReportRequest
)
from wellness_center.tools_outbreak_detector import (
    OutbreakDetectorTool,
    OutbreakAnalysisRequest,
    SymptomReportRequest,
    AbsenceReportRequest,
    ComprehensiveOutbreakRequest
)
router = APIRouter()

# Create tool instances
unified_lab_manager_tool = UnifiedLabManagerTool()
child_growth_tracker_tool = ChildGrowthTrackerTool()
outbreak_detector_tool = OutbreakDetectorTool()

# --- Request Schemas ---

# Removed unused request schemas - no corresponding active endpoints



# --- Endpoints ---

# TODO: Uncomment when tools are created
# @router.post("/wellness/clinical-dashboard")
# async def generate_clinical_dashboard(request: ClinicalDashboardRequest):
#     return generate_clinical_dashboard_insight(request.data)

# @router.get("/wellness/dashboard-live")
# async def dashboard_from_saved_file():
#     with open("data/health_user_data.json", "r") as f:
#         user_data = json.load(f)
#     return generate_clinical_dashboard_insight(user_data)

# TODO: Uncomment when tools are created
# @router.post("/wellness/patient-trends")
# def get_patient_vital_trends(request: PatientVitalTrendsRequest):
#     return analyze_vital_trends(request.patient_id, request.patient_data)

# @router.post("/wellness/doctor-summary")
# def doctor_summary_endpoint(request: DoctorSummaryRequest):
#     return generate_doctor_summary_v2(request.patient_id, request.records)

# @router.post("/wellness/differential-diagnosis")
# def get_differential_diagnosis(request: DifferentialDiagnosisRequest):
#     return differential_diagnosis(vitals=request.vitals, symptoms=request.symptoms, history=request.history)

# @router.post("/wellness/discharge-summary")
# def discharge_summary_endpoint(request: DischargeRequest):
#     return generate_discharge_summary(request.dict())

# @router.post("/wellness/mental-health-screener")
# def run_mental_health_screener(request: MentalHealthRequest):
#     return mental_health_screener(request.phq9, request.gad7, request.history)

# @router.post("/wellness/unstable-detector")
# def detect_unstable_patients(request: PatientSeriesRequest):
#     return unstable_patient_detector(request.data)



@router.post("/wellness/unified-lab-manager")
async def unified_lab_manager_endpoint(request: UnifiedLabRequest):
    """
    Unified Lab Manager - Dashboard & Task Summary (All-in-One)

    Handles both lab insights dashboard and task summary functionality:

    """
    try:
        # Use the unified tool to process the request
        result = unified_lab_manager_tool.process_unified_request(request)
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to process unified lab manager request"
        }


@router.post("/wellness/auto-test-explainer")
async def auto_test_explainer_endpoint(request: AutoTestExplainerRequest):
    """
    Auto Test Result Explainer for Internal QA (Simplified)

    Analyzes uploaded test results

    **Required fields:** technician_id, patient_demographics (basic), test_results only!
    """
    try:
        # Use the tool to analyze test results for quality control
        result = auto_test_explainer_tool.analyze_test_results(request)

        # Return the result directly
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze test results for quality control"
        }


# --- Child Growth Tracker Endpoints ---

@router.post("/wellness/child-growth/create-profile")
async def create_child_profile_endpoint(request: ChildProfileRequest):
    """
    Create Child Profile

    Initialize a new child profile for growth tracking.
    """
    try:
        result = child_growth_tracker_tool.create_child_profile(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to create child profile"
        }


@router.post("/wellness/child-growth/add-measurement")
async def add_growth_measurement_endpoint(request: GrowthMeasurementRequest):
    """
    Add Growth Measurement

    Record height and weight measurements for growth tracking.
    """
    try:
        result = child_growth_tracker_tool.add_growth_measurement(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add growth measurement"
        }


@router.post("/wellness/child-growth/add-milestone")
async def add_milestone_endpoint(request: MilestoneRequest):
    """
    Record Developmental Milestone

    Track developmental achievements and milestones.
    """
    try:
        result = child_growth_tracker_tool.add_milestone(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to record milestone"
        }


@router.post("/wellness/child-growth/add-vaccination")
async def add_vaccination_endpoint(request: VaccinationRequest):
    """
    Record Vaccination

    Track vaccination records and schedule.
    """
    try:
        result = child_growth_tracker_tool.add_vaccination(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to record vaccination"
        }


@router.post("/wellness/child-growth/analyze")
async def analyze_growth_endpoint(request: GrowthAnalysisRequest):
    """
    Analyze Growth Patterns

    Comprehensive growth analysis with percentiles and recommendations.
    """
    try:
        result = child_growth_tracker_tool.analyze_growth(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze growth patterns"
        }


@router.post("/wellness/child-growth/predict")
async def predict_growth_endpoint(request: GrowthPredictionRequest):
    """
    Predict Future Growth

    Predict future height and weight based on current trends.
    """
    try:
        result = child_growth_tracker_tool.predict_growth(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to predict growth"
        }


@router.post("/wellness/child-growth/add-photo")
async def add_photo_memory_endpoint(request: PhotoMemoryRequest):
    """
    Add Photo Memory

    Store photo memories with captions for the child's growth journey.
    """
    try:
        result = child_growth_tracker_tool.add_photo_memory(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add photo memory"
        }


@router.post("/wellness/child-growth/add-doctor-note")
async def add_doctor_note_endpoint(request: DoctorNoteRequest):
    """
    Add Doctor Note

    Record pediatrician visit notes and observations.
    """
    try:
        result = child_growth_tracker_tool.add_doctor_note(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add doctor note"
        }


@router.post("/wellness/child-growth/add-reminder")
async def add_reminder_endpoint(request: ReminderRequest):
    """
    Add Reminder

    Set reminders for appointments, vaccinations, or milestones.
    """
    try:
        result = child_growth_tracker_tool.add_reminder(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add reminder"
        }


@router.post("/wellness/child-growth/full-report")
async def generate_full_report_endpoint(request: FullReportRequest):
    """
    Generate Comprehensive Growth Report

    Complete growth report with analysis, charts, and recommendations.
    """
    try:
        result = child_growth_tracker_tool.generate_full_report(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate growth report"
        }


# --- Outbreak Detector Endpoints ---

@router.post("/wellness/outbreak/add-symptom-report")
async def add_symptom_report_endpoint(request: SymptomReportRequest):
    """
    Add Symptom Report

    Record symptom reports for disease surveillance and outbreak detection.
    """
    try:
        result = outbreak_detector_tool.add_symptom_report(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add symptom report"
        }


@router.post("/wellness/outbreak/add-absence-record")
async def add_absence_record_endpoint(request: AbsenceReportRequest):
    """
    Add Absence Record

    Record student absence data for outbreak pattern analysis.
    """
    try:
        result = outbreak_detector_tool.add_absence_record(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to add absence record"
        }


@router.post("/wellness/outbreak/analyze-risk")
async def analyze_outbreak_risk_endpoint(request: OutbreakAnalysisRequest):
    """
    Analyze Outbreak Risk

    Analyze current outbreak risk using stored surveillance data.
    """
    try:
        result = outbreak_detector_tool.analyze_outbreak_risk(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze outbreak risk"
        }


@router.post("/wellness/outbreak/comprehensive-analysis")
async def comprehensive_outbreak_analysis_endpoint(request: ComprehensiveOutbreakRequest):
    """
    Comprehensive Outbreak Analysis

    Perform complete outbreak analysis with provided symptom and absence data.
    """
    try:
        result = outbreak_detector_tool.comprehensive_outbreak_analysis(request)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to perform comprehensive outbreak analysis"
        }


@router.get("/wellness/outbreak/surveillance-summary")
async def get_surveillance_summary_endpoint():
    """
    Get Surveillance Summary

    Retrieve summary of current surveillance data and monitoring status.
    """
    try:
        result = outbreak_detector_tool.get_surveillance_summary()
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get surveillance summary"
        }


@router.delete("/wellness/outbreak/clear-data")
async def clear_surveillance_data_endpoint():
    """
    Clear Surveillance Data

    Clear all stored symptom reports and absence records.
    """
    try:
        result = outbreak_detector_tool.clear_surveillance_data()
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to clear surveillance data"
        }


