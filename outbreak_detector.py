"""
AI Disease Outbreak Detector for Schools - Proactive Health Monitoring

Tracks absenteeism patterns and symptom reports to detect early signs of infectious disease outbreaks.
Supports real-time alerts, contact tracing, and predictive analytics for schools.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from collections import Counter, defaultdict
from pydantic import BaseModel, Field, field_validator

# Optional imports for advanced analytics
try:
    import numpy as np
    from sklearn.cluster import DBSCAN  # For outbreak clustering
    ADVANCED_ANALYTICS_AVAILABLE = True
except ImportError:
    ADVANCED_ANALYTICS_AVAILABLE = False
    np = None
    DBSCAN = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SymptomReport(BaseModel):
    student_id: str
    date_reported: str  # YYYY-MM-DD
    symptoms: List[str]  # ["fever", "cough", "rash"]
    grade_level: str
    classroom: Optional[str] = None
    severity: Optional[str] = "mild"  # mild/moderate/severe
    reporter: Optional[str] = None  # teacher/nurse/parent

    @field_validator('date_reported')
    @classmethod
    def validate_date(cls, v):
        datetime.strptime(v, '%Y-%m-%d')  # Validates format
        return v

class AbsenceRecord(BaseModel):
    student_id: str
    date: str
    reason: Optional[str] = None  # "sick", "vacation", etc.
    reported_by: Optional[str] = None
    grade_level: str
    classroom: str

class OutbreakDetectionRequest(BaseModel):
    school_id: str
    date_range_days: int = 7  # Default to 1 week analysis
    symptom_reports: List[SymptomReport] = []
    absence_records: List[AbsenceRecord] = []
    alert_threshold: float = 0.7  # 0-1 confidence score for alerts
    enable_contact_tracing: bool = True

class SchoolOutbreakDetector:
    """AI-powered disease outbreak detection for schools"""

    # Disease symptom patterns (expandable)
    DISEASE_PROFILES = {
        "flu": {"symptoms": ["fever", "cough", "fatigue"], "absence_spike": True},
        "norovirus": {"symptoms": ["vomiting", "diarrhea", "nausea"], "classroom_cluster": True},
        "strep_throat": {"symptoms": ["sore throat", "fever", "swollen glands"], "grade_level_spread": True},
        "covid": {"symptoms": ["fever", "cough", "loss of taste"], "airborne": True}
    }

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def detect_outbreaks(self, request: OutbreakDetectionRequest) -> Dict[str, Any]:
        """Main outbreak detection analysis"""
        try:
            # Process data
            processed_data = self._process_inputs(request)
            
            # Core analytics
            absence_analysis = self._analyze_absences(request.absence_records, request.date_range_days)
            symptom_analysis = self._analyze_symptoms(request.symptom_reports)
            
            # Outbreak detection
            outbreak_signals = self._detect_outbreak_signals(
                absence_analysis, 
                symptom_analysis,
                request.alert_threshold
            )
            
            # Contact tracing if enabled
            contact_analysis = {}
            if request.enable_contact_tracing and outbreak_signals["potential_outbreaks"]:
                contact_analysis = self._contact_tracing_analysis(
                    request.absence_records,
                    request.symptom_reports,
                    outbreak_signals["high_risk_classes"]
                )

            return {
                "success": True,
                "school_id": request.school_id,
                "analysis_date": datetime.now().isoformat(),
                "time_window_days": request.date_range_days,
                "summary_stats": {
                    "total_students_absent": absence_analysis["total_absent"],
                    "sick_absence_rate": absence_analysis["sick_rate"],
                    "symptom_reports": len(request.symptom_reports),
                    "unique_symptoms": symptom_analysis["unique_symptoms"],
                },
                "absence_trends": absence_analysis["daily_trends"],
                "symptom_clusters": symptom_analysis["symptom_clusters"],
                "potential_outbreaks": outbreak_signals["potential_outbreaks"],
                "high_risk_areas": outbreak_signals["high_risk_classes"],
                "contact_tracing": contact_analysis,
                "recommended_actions": self._generate_recommendations(outbreak_signals),
                "alert_level": self._calculate_alert_level(outbreak_signals["confidence_scores"])
            }
        except Exception as e:
            return {"error": str(e), "success": False}

    def _process_inputs(self, request: OutbreakDetectionRequest) -> Dict:
        """Validate and preprocess input data"""
        cutoff_date = datetime.now() - timedelta(days=request.date_range_days)
        
        # Filter to recent data
        recent_absences = [
            a for a in request.absence_records 
            if datetime.strptime(a.date, '%Y-%m-%d') >= cutoff_date
        ]
        
        recent_symptoms = [
            s for s in request.symptom_reports
            if datetime.strptime(s.date_reported, '%Y-%m-%d') >= cutoff_date
        ]
        
        return {
            "absences": recent_absences,
            "symptoms": recent_symptoms,
            "cutoff_date": cutoff_date
        }

    def _analyze_absences(self, absences: List[AbsenceRecord], days: int) -> Dict:
        """Analyze absence patterns"""
        sick_absences = [a for a in absences if a.reason and "sick" in a.reason.lower()]
        
        # Daily trends
        daily_counts = defaultdict(int)
        for a in sick_absences:
            daily_counts[a.date] += 1
            
        # Calculate rates
        sick_rate = len(sick_absences) / max(len(absences), 1)
        
        return {
            "total_absent": len(absences),
            "sick_absent": len(sick_absences),
            "sick_rate": round(sick_rate, 2),
            "daily_trends": sorted(
                [{"date": k, "count": v} for k, v in daily_counts.items()],
                key=lambda x: x["date"]
            )
        }

    def _analyze_symptoms(self, symptoms: List[SymptomReport]) -> Dict:
        """Analyze symptom patterns"""
        # Symptom frequency
        symptom_counter = Counter()
        for report in symptoms:
            symptom_counter.update(report.symptoms)
            
        # Cluster analysis by classroom/grade
        clusters = defaultdict(list)
        for report in symptoms:
            key = f"{report.grade_level}-{report.classroom}" if report.classroom else report.grade_level
            clusters[key].extend(report.symptoms)
            
        return {
            "symptom_frequency": dict(symptom_counter.most_common()),
            "symptom_clusters": {
                k: dict(Counter(v).most_common()) 
                for k, v in clusters.items()
            },
            "unique_symptoms": len(symptom_counter)
        }

    def _detect_outbreak_signals(self, absence_analysis: Dict, symptom_analysis: Dict, threshold: float) -> Dict:
        """Core outbreak detection logic"""
        signals = []
        confidence_scores = []
        high_risk_classes = set()
        
        # Rule 1: Sudden absence spikes
        if len(absence_analysis["daily_trends"]) >= 3:
            last_3_days = [d["count"] for d in absence_analysis["daily_trends"][-3:]]
            if all(last_3_days[i] > 1.5 * last_3_days[i-1] for i in range(1, 3)):
                signals.append("sudden_absence_spike")
                confidence_scores.append(0.8)
                
        # Rule 2: Symptom clusters matching disease profiles
        for disease, profile in self.DISEASE_PROFILES.items():
            for location, symptoms in symptom_analysis["symptom_clusters"].items():
                match_score = self._match_symptom_pattern(symptoms, profile["symptoms"])
                if match_score > threshold:
                    signals.append(f"potential_{disease}_cluster")
                    confidence_scores.append(match_score)
                    high_risk_classes.add(location)
                    
        # Rule 3: Statistical anomaly detection
        if absence_analysis["sick_rate"] > 0.15:  # 15% sick rate threshold
            signals.append("elevated_absence_rate")
            confidence_scores.append(
                min(0.9, absence_analysis["sick_rate"] / 0.3)  # Cap at 30% rate
            )
            
        return {
            "potential_outbreaks": signals,
            "confidence_scores": confidence_scores,
            "high_risk_classes": list(high_risk_classes)
        }

    def _match_symptom_pattern(self, observed_symptoms: List, expected_symptoms: List) -> float:
        """Calculate match score between observed symptoms and disease profile"""
        observed_set = set(s[0] for s in observed_symptoms)
        expected_set = set(expected_symptoms)
        intersection = observed_set.intersection(expected_set)
        return len(intersection) / len(expected_set)

    def _contact_tracing_analysis(self, absences: List[AbsenceRecord], symptoms: List[SymptomReport], high_risk_classes: List) -> Dict:
        """Identify potential exposure paths"""
        # Find all students in high-risk classes
        exposed_students = set()
        for loc in high_risk_classes:
            if "-" in loc:  # Classroom-level
                grade, room = loc.split("-")
                exposed_students.update(
                    s.student_id for s in symptoms 
                    if s.grade_level == grade and s.classroom == room
                )
            else:  # Grade-level
                exposed_students.update(
                    s.student_id for s in symptoms 
                    if s.grade_level == loc
                )
                
        # Find shared classes/activities
        contact_map = defaultdict(set)
        for student in exposed_students:
            # Find all classes this student attended before symptoms
            student_classes = set(
                f"{a.grade_level}-{a.classroom}" for a in absences 
                if a.student_id == student and a.date < datetime.now().strftime('%Y-%m-%d')
            )
            for sc in student_classes:
                contact_map[sc].add(student)
                
        return {
            "exposed_students_count": len(exposed_students),
            "high_contact_classes": [
                {"class": k, "exposed_students": len(v)} 
                for k, v in contact_map.items()
            ]
        }

    def _generate_recommendations(self, outbreak_signals: Dict) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        if not outbreak_signals["potential_outbreaks"]:
            return ["✅ No urgent actions needed - typical health patterns detected"]
            
        if "sudden_absence_spike" in outbreak_signals["potential_outbreaks"]:
            recommendations.append(
                "🚨 Implement enhanced sanitization in high-traffic areas"
            )
            
        if any("potential_" in s for s in outbreak_signals["potential_outbreaks"]):
            recommendations.extend([
                "🔍 Conduct targeted health screenings in high-risk classes",
                "📢 Issue parent notification about symptom monitoring"
            ])
            
        if outbreak_signals["high_risk_classes"]:
            rec = "📍 Isolate affected classes: " + ", ".join(outbreak_signals["high_risk_classes"])
            recommendations.append(rec)
            
        if len(outbreak_signals["potential_outbreaks"]) >= 2:
            recommendations.append(
                "⚠️ Consider temporary closure of affected grades/classes"
            )
            
        return recommendations

    def _calculate_alert_level(self, confidence_scores: List[float]) -> str:
        """Determine overall alert level"""
        if not confidence_scores:
            return "green"
            
        max_score = max(confidence_scores)
        if max_score > 0.8:
            return "red"
        elif max_score > 0.6:
            return "orange"
        elif max_score > 0.4:
            return "yellow"
        return "green"

