import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from collections import defaultdict, Counter
import base64
from io import BytesIO

# Optional imports for visualization
try:
    import numpy as np
    import matplotlib.pyplot as plt
    import pandas as pd
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False
    np = None
    plt = None
    pd = None

class ChildGrowthTracker:
    """Complete child growth and development tracking system"""
    
    def __init__(self):
        """Initialize all tracking data and standards"""
        # WHO growth standards (simplified for example)
        self.growth_standards = {
            'male': {
                '1-3': {'height': (75, 96), 'weight': (9, 14)},
                '4-10': {'height': (100, 138), 'weight': (15, 32)},
                '11-17': {'height': (142, 176), 'weight': (35, 65)}
            },
            'female': {
                '1-3': {'height': (73, 95), 'weight': (8, 14)},
                '4-10': {'height': (98, 135), 'weight': (14, 30)},
                '11-17': {'height': (140, 163), 'weight': (32, 58)}
            }
        }
        
        # Developmental milestones with activity suggestions
        self.milestones = {
            '1-3': [
                (12, "Walks independently", "Encourage walking on different surfaces"),
                (18, "Says 20+ words", "Read together daily to build vocabulary"),
                (24, "Runs and climbs", "Provide safe climbing opportunities")
            ],
            '4-10': [
                (48, "Dresses self", "Practice with buttons and zippers"),
                (60, "Counts to 10", "Count objects during daily activities"),
                (84, "Reads simple books", "Have them read to you nightly")
            ],
            '11-17': [
                (132, "Shows logical thinking", "Play strategy games together"),
                (144, "Handles complex tasks", "Assign age-appropriate responsibilities")
            ]
        }
        
        # Vaccination schedule (simplified example)
        self.vaccination_schedule = {
            '1-3': [
                (12, "MMR", "Protects against measles, mumps, rubella"),
                (15, "DTaP", "Diphtheria, tetanus, pertussis booster"),
                (18, "Hepatitis A", "First dose of 2-dose series")
            ],
            '4-10': [
                (48, "DTaP", "Preschool booster"),
                (60, "Polio", "School entry booster"),
                (120, "HPV", "Human papillomavirus vaccine (2-dose series)")
            ]
        }
        
        # Nutrition recommendations by growth status
        self.nutrition_tips = {
            'underweight': [
                "Offer healthy fats like avocado and nut butters",
                "Increase meal frequency to 5-6 small meals/day",
                "Include protein-rich foods at each meal"
            ],
            'normal': [
                "Maintain balanced diet with all food groups",
                "Limit processed foods and added sugars",
                "Ensure adequate calcium for bone growth"
            ],
            'overweight': [
                "Focus on whole foods rather than processed",
                "Increase physical activity gradually",
                "Limit sugary drinks - offer water instead"
            ]
        }
        
        # Activity suggestions by age group
        self.activity_suggestions = {
            '1-3': [
                "Tummy time and floor play (30+ minutes daily)",
                "Structured active play 3+ times daily",
                "Outdoor exploration time"
            ],
            '4-10': [
                "60 minutes of moderate-vigorous activity daily",
                "Sports or dance classes 2-3 times weekly",
                "Active family outings on weekends"
            ],
            '11-17': [
                "60+ minutes of activity daily including strength training",
                "Team sports or individual athletic activities",
                "Active transportation (walking/biking to school)"
            ]
        }
        
        # Initialize all data storage
        self.child_data = {
            'profile': None,          # Child's basic information
            'measurements': [],       # Growth measurements over time
            'milestones_achieved': [], # Developmental milestones
            'vaccinations_received': [] # Vaccination records
        }
        
        # Additional features storage
        self.photo_memories = []      # Photo memories with captions
        self.doctor_notes = []       # Pediatrician visit notes
        self.reminders = []          # Important reminders
        self.custom_notes = []       # Parent's personal notes

    # Core Profile Methods
    def create_child_profile(self, name: str, birth_date: str, gender: str) -> str:
        """Initialize a new child profile"""
        self.child_data['profile'] = {
            'name': name,
            'birth_date': birth_date,
            'gender': gender,
            'last_updated': datetime.now().strftime('%Y-%m-%d')
        }
        return f"Profile created for {name}"

    def get_child_age(self, date_str: Optional[str] = None) -> float:
        """Calculate current age in months"""
        if not self.child_data['profile']:
            return 0.0
            
        ref_date = datetime.now() if date_str is None else datetime.strptime(date_str, '%Y-%m-%d')
        birth_date = datetime.strptime(self.child_data['profile']['birth_date'], '%Y-%m-%d')
        return (ref_date - birth_date).days / 30.44

    # Growth Tracking Methods
    def add_measurement(self, date: str, height: float, weight: float) -> str:
        """Record a new growth measurement"""
        age = self.get_child_age(date)
        age_group = self._get_age_group(age)
        
        self.child_data['measurements'].append({
            'date': date,
            'age_months': age,
            'height': height,
            'weight': weight,
            'bmi': round(weight / ((height/100) ** 2), 1),
            'age_group': age_group
        })
        return f"Measurement added for {date}"

    def get_growth_history(self) -> List[Dict]:
        """Return all growth measurements sorted by date"""
        return sorted(self.child_data['measurements'], key=lambda x: x['date'])

    # Developmental Milestones
    def add_milestone(self, milestone: str, date: str) -> str:
        """Record a developmental achievement"""
        age = self.get_child_age(date)
        self.child_data['milestones_achieved'].append({
            'milestone': milestone,
            'date': date,
            'age_months': age
        })
        return f"Milestone '{milestone}' recorded"

    def get_upcoming_milestones(self) -> List[Dict]:
        """Get expected milestones not yet achieved"""
        if not self.child_data['measurements']:
            return []
            
        latest_age = self.child_data['measurements'][-1]['age_months']
        age_group = self._get_age_group(latest_age)
        achieved = set(m['milestone'] for m in self.child_data['milestones_achieved'])
        
        upcoming = []
        for age, milestone, suggestion in self.milestones.get(age_group, []):
            if age > latest_age - 3 and milestone not in achieved:  # Include slightly delayed
                upcoming.append({
                    'milestone': milestone,
                    'expected_age': f"{int(age//12)}y {int(age%12)}m",
                    'preparation_tip': suggestion,
                    'status': 'due_now' if age <= latest_age else 'upcoming'
                })
        
        return sorted(upcoming, key=lambda x: float(x['expected_age'].split('y')[0]))

    # Vaccination Tracking
    def add_vaccination(self, vaccine: str, date: str) -> str:
        """Record a vaccination"""
        age = self.get_child_age(date)
        self.child_data['vaccinations_received'].append({
            'vaccine': vaccine,
            'date': date,
            'age_months': age
        })
        return f"Vaccination '{vaccine}' recorded"

    def get_due_vaccinations(self) -> List[Dict]:
        """Get vaccinations that are due or upcoming"""
        if not self.child_data['measurements']:
            return []
            
        latest_age = self.child_data['measurements'][-1]['age_months']
        age_group = self._get_age_group(latest_age)
        received = set(v['vaccine'] for v in self.child_data['vaccinations_received'])
        
        due = []
        for age, vaccine, description in self.vaccination_schedule.get(age_group, []):
            if age > latest_age - 3 and vaccine not in received:  # Include slightly overdue
                due.append({
                    'vaccine': vaccine,
                    'recommended_age': f"{int(age//12)}y {int(age%12)}m",
                    'description': description,
                    'status': 'due_now' if age <= latest_age else 'upcoming'
                })
        
        return sorted(due, key=lambda x: float(x['recommended_age'].split('y')[0]))

    # Health Recommendations
    def get_nutrition_recommendations(self) -> List[str]:
        """Get nutrition tips based on current growth status"""
        if not self.child_data['measurements']:
            return []
            
        latest = self.child_data['measurements'][-1]
        bmi = latest['bmi']
        
        if bmi < 16: 
            return self.nutrition_tips['underweight']
        elif bmi > 22: 
            return self.nutrition_tips['overweight']
        return self.nutrition_tips['normal']

    def get_activity_suggestions(self) -> List[str]:
        """Get age-appropriate activity suggestions"""
        if not self.child_data['measurements']:
            return []
            
        age_group = self.child_data['measurements'][-1]['age_group']
        return self.activity_suggestions.get(age_group, [])

    # New Features: Photo Memories
    def add_photo_memory(self, image_data: bytes, caption: str, date: str) -> str:
        """Add a photo memory with caption"""
        self.photo_memories.append({
            'date': date,
            'caption': caption,
            'image': base64.b64encode(image_data).decode('utf-8')
        })
        return "Photo memory added"

    def get_recent_photos(self, count: int = 3) -> List[Dict]:
        """Get most recent photo memories"""
        return sorted(self.photo_memories, key=lambda x: x['date'], reverse=True)[:count]

    # New Features: Doctor Notes
    def add_doctor_note(self, date: str, note: str, doctor: str = "") -> str:
        """Add pediatrician visit notes"""
        self.doctor_notes.append({
            'date': date,
            'doctor': doctor,
            'note': note
        })
        return "Doctor note added"

    def get_doctor_notes(self) -> List[Dict]:
        """Get all doctor notes sorted by date"""
        return sorted(self.doctor_notes, key=lambda x: x['date'], reverse=True)

    # New Features: Reminders
    def add_reminder(self, reminder_type: str, due_date: str, notes: str = "") -> str:
        """Add a new reminder"""
        self.reminders.append({
            'type': reminder_type,
            'due_date': due_date,
            'notes': notes,
            'completed': False
        })
        return "Reminder added"

    def get_upcoming_reminders(self, count: int = 5) -> List[Dict]:
        """Get upcoming reminders sorted by due date"""
        upcoming = [r for r in self.reminders if not r['completed']]
        return sorted(upcoming, key=lambda x: x['due_date'])[:count]

    def complete_reminder(self, index: int) -> bool:
        """Mark a reminder as completed"""
        if 0 <= index < len(self.reminders):
            self.reminders[index]['completed'] = True
            return True
        return False

    # Growth Analysis and Reporting
    def analyze_growth(self) -> Dict:
        """Analyze current growth patterns and percentiles"""
        if not self.child_data['profile'] or not self.child_data['measurements']:
            return {"error": "Insufficient data for analysis"}
            
        latest = sorted(self.child_data['measurements'], key=lambda x: x['date'])[-1]
        profile = self.child_data['profile']
        age_group = latest['age_group']
        
        # Calculate percentiles
        standards = self.growth_standards[profile['gender']][age_group]
        height_percentile = self._calculate_percentile(latest['height'], standards['height'])
        weight_percentile = self._calculate_percentile(latest['weight'], standards['weight'])
        
        # Calculate velocity if we have multiple measurements
        velocity = {}
        measurements = self.get_growth_history()
        if len(measurements) > 1:
            prev = measurements[-2]
            days = (datetime.strptime(latest['date'], '%Y-%m-%d') - 
                   datetime.strptime(prev['date'], '%Y-%m-%d')).days
            velocity = {
                'height_cm_per_month': round((latest['height'] - prev['height']) / days * 30.44, 2),
                'weight_kg_per_month': round((latest['weight'] - prev['weight']) / days * 30.44, 2)
            }
        
        # Determine BMI category
        bmi_category = 'normal'
        if latest['bmi'] < 16: bmi_category = 'underweight'
        elif latest['bmi'] > 22: bmi_category = 'overweight'
        
        return {
            'age_months': latest['age_months'],
            'height': latest['height'],
            'height_percentile': height_percentile,
            'weight': latest['weight'],
            'weight_percentile': weight_percentile,
            'bmi': latest['bmi'],
            'bmi_category': bmi_category,
            'growth_velocity': velocity,
            'nutrition_recommendations': self.get_nutrition_recommendations(),
            'activity_suggestions': self.get_activity_suggestions()
        }

    def predict_growth(self, target_age_months: float) -> Dict:
        """Predict future growth based on current trends"""
        if len(self.child_data['measurements']) < 2:
            return {"error": "Need at least 2 measurements for prediction"}

        if not VISUALIZATION_AVAILABLE:
            return {"error": "Prediction requires numpy for calculations"}

        # Simple linear regression
        measurements = self.get_growth_history()
        ages = [m['age_months'] for m in measurements]
        heights = [m['height'] for m in measurements]
        weights = [m['weight'] for m in measurements]

        height_slope = np.polyfit(ages, heights, 1)[0]
        weight_slope = np.polyfit(ages, weights, 1)[0]

        last = measurements[-1]
        months_diff = target_age_months - last['age_months']

        return {
            'current_age': last['age_months'],
            'target_age': target_age_months,
            'predicted_height': round(last['height'] + height_slope * months_diff, 1),
            'predicted_weight': round(last['weight'] + weight_slope * months_diff, 1)
        }

    def generate_growth_chart(self) -> Optional[str]:
        """Generate growth chart image as base64 string"""
        if len(self.child_data['measurements']) < 2:
            return None

        if not VISUALIZATION_AVAILABLE:
            return None  # Return None if visualization libraries not available

        df = pd.DataFrame(self.child_data['measurements'])
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))

        # Height chart
        ax1.plot(df['date'], df['height'], 'b-o', label='Height')
        ax1.set_ylabel('Height (cm)')
        ax1.grid(True)
        ax1.set_title(f"{self.child_data['profile']['name']}'s Growth Chart")

        # Weight chart
        ax2.plot(df['date'], df['weight'], 'g-s', label='Weight')
        ax2.set_ylabel('Weight (kg)')
        ax2.set_xlabel('Date')
        ax2.grid(True)

        plt.tight_layout()

        # Save to buffer
        buf = BytesIO()
        plt.savefig(buf, format='png', dpi=150)
        plt.close()
        buf.seek(0)

        return base64.b64encode(buf.read()).decode('utf-8')

    def generate_full_report(self) -> Dict:
        """Generate comprehensive report with all data"""
        if not self.child_data['profile']:
            return {"error": "No child profile exists"}
            
        profile = self.child_data['profile']
        analysis = self.analyze_growth()
        
        if 'error' in analysis:
            return analysis
            
        return {
            'child_name': profile['name'],
            'birth_date': profile['birth_date'],
            'gender': profile['gender'],
            'current_age': analysis['age_months'],
            'growth_metrics': {
                'height': analysis['height'],
                'height_percentile': analysis['height_percentile'],
                'weight': analysis['weight'],
                'weight_percentile': analysis['weight_percentile'],
                'bmi': analysis['bmi'],
                'bmi_category': analysis['bmi_category'],
                'growth_velocity': analysis.get('growth_velocity', {})
            },
            'development': {
                'achieved_milestones': [m['milestone'] for m in self.child_data['milestones_achieved']],
                'upcoming_milestones': self.get_upcoming_milestones()
            },
            'health': {
                'vaccinations_received': [v['vaccine'] for v in self.child_data['vaccinations_received']],
                'due_vaccinations': self.get_due_vaccinations(),
                'nutrition_recommendations': analysis['nutrition_recommendations'],
                'activity_suggestions': analysis['activity_suggestions']
            },
            'extras': {
                'photo_count': len(self.photo_memories),
                'doctor_notes_count': len(self.doctor_notes),
                'upcoming_reminders': self.get_upcoming_reminders()
            },
            'growth_chart': self.generate_growth_chart()
        }

    # Helper Methods
    def _get_age_group(self, age_months: float) -> str:
        """Categorize child into age group"""
        if age_months <= 36: return '1-3'
        if age_months <= 120: return '4-10'
        return '11-17'

    def _calculate_percentile(self, value: float, range: tuple) -> str:
        """Estimate percentile based on standard ranges"""
        min_val, max_val = range
        position = (value - min_val) / (max_val - min_val)
        if position < 0.1: return "<10th"
        elif position < 0.25: return "10-25th"
        elif position < 0.5: return "25-50th"
        elif position < 0.75: return "50-75th"
        elif position < 0.9: return "75-90th"
        return ">90th"

    def save_data(self, filepath: str) -> bool:
        """Save all tracker data to JSON file"""
        try:
            with open(filepath, 'w') as f:
                json.dump({
                    'child_data': self.child_data,
                    'photo_memories': self.photo_memories,
                    'doctor_notes': self.doctor_notes,
                    'reminders': self.reminders
                }, f)
            return True
        except:
            return False

    def load_data(self, filepath: str) -> bool:
        """Load tracker data from JSON file"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                self.child_data = data.get('child_data', {})
                self.photo_memories = data.get('photo_memories', [])
                self.doctor_notes = data.get('doctor_notes', [])
                self.reminders = data.get('reminders', [])
            return True
        except:
            return False
