"""Outbreak Detector - Ultra-Optimized Disease Surveillance Tool"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field
from collections import Counter, defaultdict
import sys, os

# Import core outbreak detector
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from outbreak_detector import SchoolOutbreakDetector, OutbreakDetectionRequest, SymptomReport, AbsenceRecord

# --- Elite Request Models ---
class OutbreakAnalysisRequest(BaseModel):
    school_id: str = Field(..., description="School or facility identifier")
    date_range_days: int = Field(default=7, description="Number of days to analyze")
    alert_threshold: float = Field(default=0.7, description="Alert confidence threshold (0-1)")
    enable_contact_tracing: bool = Field(default=True, description="Enable contact tracing analysis")

class SymptomReportRequest(BaseModel):
    student_id: str = Field(..., description="Student identifier")
    date_reported: str = Field(..., description="Report date in YYYY-MM-DD format")
    symptoms: List[str] = Field(..., description="List of symptoms")
    grade_level: str = Field(..., description="Student's grade level")
    classroom: Optional[str] = Field(default=None, description="Classroom identifier")
    severity: Optional[str] = Field(default="mild", description="Symptom severity (mild/moderate/severe)")
    reporter: Optional[str] = Field(default=None, description="Who reported (teacher/nurse/parent)")

class AbsenceReportRequest(BaseModel):
    student_id: str = Field(..., description="Student identifier")
    date_absent: str = Field(..., description="Absence date in YYYY-MM-DD format")
    reason: str = Field(..., description="Reason for absence")
    grade_level: str = Field(..., description="Student's grade level")
    classroom: Optional[str] = Field(default=None, description="Classroom identifier")
    duration_days: int = Field(default=1, description="Expected duration of absence")

class ComprehensiveOutbreakRequest(BaseModel):
    school_id: str = Field(..., description="School or facility identifier")
    symptom_reports: List[SymptomReportRequest] = Field(default=[], description="Symptom reports to analyze")
    absence_reports: List[AbsenceReportRequest] = Field(default=[], description="Absence reports to analyze")
    date_range_days: int = Field(default=14, description="Analysis period in days")
    alert_threshold: float = Field(default=0.7, description="Alert confidence threshold")
    include_predictions: bool = Field(default=True, description="Include outbreak predictions")
    include_recommendations: bool = Field(default=True, description="Include actionable recommendations")

# --- Tool Class ---
class OutbreakDetectorTool:
    """Outbreak detector with minimal code, maximum surveillance functionality"""
    
    def __init__(self):
        self.name = "outbreak_detector_tool"
        self.description = "Elite AI-powered disease outbreak detection system"
        self.detector = SchoolOutbreakDetector()

        # Internal storage for symptom reports and absence records
        self.symptom_reports = []
        self.absence_records = []

        # disease patterns for rapid detection
        self.disease_patterns = {
            "flu": {"symptoms": ["fever", "cough", "fatigue", "body_aches"], "incubation": 2, "contagious_period": 7},
            "covid": {"symptoms": ["fever", "cough", "loss_of_taste", "shortness_of_breath"], "incubation": 5, "contagious_period": 10},
            "stomach_bug": {"symptoms": ["nausea", "vomiting", "diarrhea", "stomach_pain"], "incubation": 1, "contagious_period": 3},
            "strep_throat": {"symptoms": ["sore_throat", "fever", "headache", "swollen_glands"], "incubation": 3, "contagious_period": 5},
            "common_cold": {"symptoms": ["runny_nose", "sneezing", "cough", "congestion"], "incubation": 2, "contagious_period": 7}
        }
    
    def add_symptom_report(self, request: SymptomReportRequest) -> Dict[str, Any]:
        """Symptom reporting with instant analysis"""
        try:
            # Create symptom report
            symptom_report = SymptomReport(
                student_id=request.student_id,
                date_reported=request.date_reported,
                symptoms=request.symptoms,
                grade_level=request.grade_level,
                classroom=request.classroom,
                severity=request.severity
            )
            
            # Add to internal storage
            self.symptom_reports.append(symptom_report)
            
            # Elite instant analysis
            disease_match = self._match_disease_pattern(request.symptoms)
            risk_assessment = self._assess_individual_risk(request)
            
            return {
                "success": True,
                "message": f"Symptom report added for student {request.student_id}",
                "symptom_report": {
                    "student_id": request.student_id,
                    "date_reported": request.date_reported,
                    "symptoms": request.symptoms,
                    "severity": request.severity,
                    "grade_level": request.grade_level,
                    "classroom": request.classroom
                },
                "instant_analysis": {
                    "disease_match": disease_match,
                    "risk_level": risk_assessment["risk_level"],
                    "contagion_potential": risk_assessment["contagion_potential"],
                    "recommended_actions": risk_assessment["actions"]
                },
                "total_reports": len(self.symptom_reports)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_absence_record(self, request: AbsenceReportRequest) -> Dict[str, Any]:
        """Absence recording with pattern detection"""
        try:
            # Create absence record
            absence_record = AbsenceRecord(
                student_id=request.student_id,
                date=request.date_absent,
                reason=request.reason,
                grade_level=request.grade_level,
                classroom=request.classroom
            )
            
            # Add to internal storage
            self.absence_records.append(absence_record)
            
            #pattern analysis
            absence_pattern = self._analyze_absence_pattern(request)
            cluster_risk = self._assess_cluster_risk(request.classroom, request.grade_level)
            
            return {
                "success": True,
                "message": f"Absence record added for student {request.student_id}",
                "absence_record": {
                    "student_id": request.student_id,
                    "date_absent": request.date_absent,
                    "reason": request.reason,
                    "duration_days": request.duration_days,
                    "grade_level": request.grade_level,
                    "classroom": request.classroom
                },
                "pattern_analysis": {
                    "absence_trend": absence_pattern["trend"],
                    "cluster_risk": cluster_risk,
                    "similar_cases": absence_pattern["similar_cases"],
                    "alert_level": absence_pattern["alert_level"]
                },
                "total_absences": len(self.absence_records)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def analyze_outbreak_risk(self, request: OutbreakAnalysisRequest) -> Dict[str, Any]:
        """Outbreak risk analysis with ML-powered insights"""
        try:
            # Create detection request with stored data
            detection_request = OutbreakDetectionRequest(
                school_id=request.school_id,
                symptom_reports=self.symptom_reports,
                absence_records=self.absence_records,
                date_range_days=request.date_range_days,
                alert_threshold=request.alert_threshold,
                enable_contact_tracing=request.enable_contact_tracing
            )

            # Run core analysis
            analysis_result = self.detector.detect_outbreaks(detection_request)
            
            #enhancements
            enhanced_analysis = {
                **analysis_result,
                "elite_insights": self._generate_elite_insights(analysis_result),
                "contact_tracing": self._perform_contact_tracing() if request.enable_contact_tracing else None,
                "predictive_modeling": self._run_predictive_models(analysis_result),
                "intervention_recommendations": self._generate_interventions(analysis_result),
                "communication_templates": self._generate_communication_templates(analysis_result)
            }
            
            return {"success": True, "outbreak_analysis": enhanced_analysis}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def comprehensive_outbreak_analysis(self, request: ComprehensiveOutbreakRequest) -> Dict[str, Any]:
        """Comprehensive analysis with full surveillance capabilities"""
        try:
            # Convert and store symptom reports
            temp_symptom_reports = []
            for symptom_request in request.symptom_reports:
                symptom_report = SymptomReport(
                    student_id=symptom_request.student_id,
                    date_reported=symptom_request.date_reported,
                    symptoms=symptom_request.symptoms,
                    grade_level=symptom_request.grade_level,
                    classroom=symptom_request.classroom,
                    severity=symptom_request.severity,
                    reporter=symptom_request.reporter
                )
                temp_symptom_reports.append(symptom_report)

            # Convert and store absence reports
            temp_absence_records = []
            for absence_request in request.absence_reports:
                absence_record = AbsenceRecord(
                    student_id=absence_request.student_id,
                    date=absence_request.date_absent,
                    reason=absence_request.reason,
                    grade_level=absence_request.grade_level,
                    classroom=absence_request.classroom
                )
                temp_absence_records.append(absence_record)

            # Run comprehensive analysis with all data (stored + provided)
            all_symptom_reports = self.symptom_reports + temp_symptom_reports
            all_absence_records = self.absence_records + temp_absence_records

            detection_request = OutbreakDetectionRequest(
                school_id=request.school_id,
                symptom_reports=all_symptom_reports,
                absence_records=all_absence_records,
                date_range_days=request.date_range_days,
                alert_threshold=request.alert_threshold,
                enable_contact_tracing=True
            )

            base_analysis = self.detector.detect_outbreaks(detection_request)
            
            #comprehensive enhancements
            comprehensive_result = {
                "school_id": request.school_id,
                "analysis_timestamp": datetime.now().isoformat(),
                "data_summary": {
                    "symptom_reports_processed": len(request.symptom_reports),
                    "absence_reports_processed": len(request.absence_reports),
                    "total_students_affected": len(set([r.student_id for r in request.symptom_reports] + [r.student_id for r in request.absence_reports])),
                    "analysis_period_days": request.date_range_days
                },
                "outbreak_analysis": base_analysis,
                "disease_identification": self._identify_likely_diseases(request.symptom_reports),
                "spatial_analysis": self._perform_spatial_analysis(request.symptom_reports, request.absence_reports),
                "temporal_analysis": self._perform_temporal_analysis(request.symptom_reports, request.absence_reports),
                "severity_assessment": self._assess_outbreak_severity(base_analysis, request.symptom_reports),
                "predictions": self._generate_outbreak_predictions(base_analysis) if request.include_predictions else None,
                "recommendations": self._generate_comprehensive_recommendations(base_analysis, request.symptom_reports, request.absence_reports) if request.include_recommendations else None
            }
            
            return {"success": True, "comprehensive_analysis": comprehensive_result}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    # --- Helper Methods ---
    def _match_disease_pattern(self, symptoms: List[str]) -> Dict[str, Any]:
        """Disease pattern matching with confidence scoring"""
        symptom_set = set(s.lower().replace(" ", "_") for s in symptoms)
        matches = {}
        
        for disease, pattern in self.disease_patterns.items():
            pattern_symptoms = set(pattern["symptoms"])
            overlap = len(symptom_set.intersection(pattern_symptoms))
            confidence = overlap / len(pattern_symptoms) if pattern_symptoms else 0
            
            if confidence > 0:
                matches[disease] = {
                    "confidence": round(confidence, 2),
                    "matching_symptoms": list(symptom_set.intersection(pattern_symptoms)),
                    "incubation_period": pattern["incubation"],
                    "contagious_period": pattern["contagious_period"]
                }
        
        best_match = max(matches.items(), key=lambda x: x[1]["confidence"]) if matches else None
        
        return {
            "best_match": best_match[0] if best_match else "unknown",
            "confidence": best_match[1]["confidence"] if best_match else 0,
            "all_matches": matches
        }
    
    def _assess_individual_risk(self, request: SymptomReportRequest) -> Dict[str, Any]:
        """Individual risk assessment"""
        severity_scores = {"mild": 1, "moderate": 2, "severe": 3}
        severity_score = severity_scores.get(request.severity, 1)
        
        symptom_count = len(request.symptoms)
        risk_score = (severity_score * 2) + symptom_count
        
        if risk_score >= 8:
            risk_level = "high"
            actions = ["Immediate isolation", "Contact health authorities", "Deep clean classroom"]
        elif risk_score >= 5:
            risk_level = "medium"
            actions = ["Monitor closely", "Increase sanitization", "Notify parents"]
        else:
            risk_level = "low"
            actions = ["Continue monitoring", "Standard precautions"]
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "contagion_potential": "high" if severity_score >= 2 and symptom_count >= 3 else "medium" if severity_score >= 2 or symptom_count >= 3 else "low",
            "actions": actions
        }
    
    def _analyze_absence_pattern(self, request: AbsenceReportRequest) -> Dict[str, Any]:
        """Absence pattern analysis"""
        # Analyze recent absences for similar patterns
        recent_absences = [r for r in self.absence_records
                          if (datetime.now() - datetime.strptime(r.date, "%Y-%m-%d")).days <= 7]
        
        similar_reasons = [r for r in recent_absences if request.reason.lower() in r.reason.lower()]
        same_classroom = [r for r in recent_absences if r.classroom == request.classroom]
        
        alert_level = "high" if len(similar_reasons) >= 3 or len(same_classroom) >= 5 else "medium" if len(similar_reasons) >= 2 or len(same_classroom) >= 3 else "low"
        
        return {
            "trend": "increasing" if len(similar_reasons) > 1 else "stable",
            "similar_cases": len(similar_reasons),
            "classroom_absences": len(same_classroom),
            "alert_level": alert_level
        }
    
    def _assess_cluster_risk(self, classroom: Optional[str], grade_level: str) -> str:
        """Custer risk assessment"""
        if not classroom:
            return "unknown"

        recent_reports = [r for r in self.symptom_reports
                         if (datetime.now() - datetime.strptime(r.date_reported, "%Y-%m-%d")).days <= 3]
        
        classroom_cases = [r for r in recent_reports if r.classroom == classroom]
        grade_cases = [r for r in recent_reports if r.grade_level == grade_level]
        
        if len(classroom_cases) >= 3:
            return "high"
        elif len(classroom_cases) >= 2 or len(grade_cases) >= 5:
            return "medium"
        else:
            return "low"
    
    def _generate_elite_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Insight generation"""
        insights = []
        
        confidence = analysis.get("confidence_score", 0)
        if confidence >= 0.8:
            insights.append("High probability outbreak detected - immediate action required")
        elif confidence >= 0.6:
            insights.append("Moderate outbreak risk - enhanced monitoring recommended")
        
        alert_level = analysis.get("alert_level", "low")
        if alert_level == "critical":
            insights.append("Critical alert level reached - contact health authorities")
        
        return insights
    
    def _perform_contact_tracing(self) -> Dict[str, Any]:
        """Contact tracing analysis"""
        # Simplified contact tracing based on classroom and grade proximity
        contacts = defaultdict(list)
        
        for report in self.symptom_reports:
            if report.classroom:
                classroom_contacts = [r.student_id for r in self.symptom_reports
                                    if r.classroom == report.classroom and r.student_id != report.student_id]
                contacts[report.student_id].extend(classroom_contacts)
        
        return {
            "total_contacts_identified": sum(len(contact_list) for contact_list in contacts.values()),
            "high_risk_contacts": len([c for contact_list in contacts.values() for c in contact_list if len(contact_list) >= 3]),
            "contact_network_size": len(contacts)
        }
    
    def _run_predictive_models(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Predictive modeling"""
        confidence = analysis.get("confidence_score", 0)
        current_cases = analysis.get("total_cases", 0)
        
        # Simple exponential growth model
        growth_rate = 1.2 if confidence >= 0.7 else 1.1 if confidence >= 0.5 else 1.05
        
        predictions = {}
        for days in [3, 7, 14]:
            predicted_cases = int(current_cases * (growth_rate ** days))
            predictions[f"day_{days}"] = {
                "predicted_cases": predicted_cases,
                "confidence": "high" if confidence >= 0.7 else "medium" if confidence >= 0.5 else "low"
            }
        
        return predictions
    
    def _generate_interventions(self, analysis: Dict[str, Any]) -> List[str]:
        """Intervention recommendations"""
        interventions = []
        
        confidence = analysis.get("confidence_score", 0)
        alert_level = analysis.get("alert_level", "low")
        
        if alert_level == "critical" or confidence >= 0.8:
            interventions.extend([
                "Implement immediate school closure protocols",
                "Contact local health department",
                "Begin contact tracing procedures",
                "Issue public health advisory"
            ])
        elif alert_level == "high" or confidence >= 0.6:
            interventions.extend([
                "Increase cleaning and sanitization",
                "Implement social distancing measures",
                "Monitor attendance closely",
                "Prepare communication to parents"
            ])
        else:
            interventions.extend([
                "Continue standard monitoring",
                "Maintain hygiene protocols",
                "Document all cases carefully"
            ])
        
        return interventions[:5]
    
    def _generate_communication_templates(self, analysis: Dict[str, Any]) -> Dict[str, str]:
        """Communication template generation"""
        alert_level = analysis.get("alert_level", "low")
        
        templates = {
            "parent_notification": f"Dear Parents, We are monitoring a potential health situation at our school. Current alert level: {alert_level}. Please keep your child home if they show any symptoms.",
            "staff_alert": f"Staff Alert: Enhanced health monitoring in effect. Alert level: {alert_level}. Please report any student symptoms immediately.",
            "health_department": f"Health Department Notification: Potential outbreak detected. Alert level: {alert_level}. Requesting guidance on next steps."
        }
        
        return templates
    
    def _identify_likely_diseases(self, symptom_reports: List[SymptomReportRequest]) -> Dict[str, Any]:
        """Elite disease identification across all reports"""
        all_symptoms = []
        for report in symptom_reports:
            all_symptoms.extend(report.symptoms)
        
        symptom_frequency = Counter(s.lower().replace(" ", "_") for s in all_symptoms)
        
        disease_scores = {}
        for disease, pattern in self.disease_patterns.items():
            score = sum(symptom_frequency.get(symptom, 0) for symptom in pattern["symptoms"])
            if score > 0:
                disease_scores[disease] = score
        
        most_likely = max(disease_scores.items(), key=lambda x: x[1]) if disease_scores else ("unknown", 0)
        
        return {
            "most_likely_disease": most_likely[0],
            "confidence_score": most_likely[1] / len(symptom_reports) if symptom_reports else 0,
            "all_disease_scores": disease_scores,
            "symptom_frequency": dict(symptom_frequency)
        }
    
    def _perform_spatial_analysis(self, symptom_reports: List[SymptomReportRequest], absence_reports: List[AbsenceReportRequest]) -> Dict[str, Any]:
        """Spatial clustering analysis"""
        classroom_cases = Counter()
        grade_cases = Counter()
        
        for report in symptom_reports:
            if report.classroom:
                classroom_cases[report.classroom] += 1
            grade_cases[report.grade_level] += 1
        
        for report in absence_reports:
            if report.classroom:
                classroom_cases[report.classroom] += 1
            grade_cases[report.grade_level] += 1
        
        hotspots = [classroom for classroom, count in classroom_cases.items() if count >= 3]
        
        return {
            "classroom_hotspots": hotspots,
            "most_affected_classroom": classroom_cases.most_common(1)[0] if classroom_cases else None,
            "most_affected_grade": grade_cases.most_common(1)[0] if grade_cases else None,
            "spatial_clustering": "high" if len(hotspots) >= 2 else "medium" if len(hotspots) == 1 else "low"
        }
    
    def _perform_temporal_analysis(self, symptom_reports: List[SymptomReportRequest], absence_reports: List[AbsenceReportRequest]) -> Dict[str, Any]:
        """Temporal pattern analysis"""
        dates = []
        for report in symptom_reports:
            dates.append(report.date_reported)
        for report in absence_reports:
            dates.append(report.date)
        
        if not dates:
            return {"trend": "no_data"}
        
        date_counts = Counter(dates)
        sorted_dates = sorted(date_counts.keys())
        
        if len(sorted_dates) >= 2:
            recent_trend = "increasing" if date_counts[sorted_dates[-1]] > date_counts[sorted_dates[0]] else "decreasing"
        else:
            recent_trend = "stable"
        
        return {
            "trend": recent_trend,
            "peak_date": date_counts.most_common(1)[0][0] if date_counts else None,
            "cases_by_date": dict(date_counts),
            "outbreak_velocity": "rapid" if len(set(dates)) <= 3 and len(dates) >= 5 else "moderate"
        }
    
    def _assess_outbreak_severity(self, analysis: Dict[str, Any], symptom_reports: List[SymptomReportRequest]) -> str:
        """Elite severity assessment"""
        severe_cases = sum(1 for report in symptom_reports if report.severity == "severe")
        total_cases = len(symptom_reports)
        confidence = analysis.get("confidence_score", 0)
        
        if severe_cases >= 3 or (confidence >= 0.8 and total_cases >= 10):
            return "severe"
        elif severe_cases >= 1 or (confidence >= 0.6 and total_cases >= 5):
            return "moderate"
        else:
            return "mild"
    
    def _generate_outbreak_predictions(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Outbreak prediction modeling"""
        confidence = analysis.get("confidence_score", 0)
        current_cases = analysis.get("total_cases", 0)
        
        # Elite prediction algorithm
        base_growth = 1.15 if confidence >= 0.7 else 1.08
        
        return {
            "3_day_forecast": {
                "expected_cases": int(current_cases * (base_growth ** 3)),
                "confidence": "high" if confidence >= 0.7 else "medium"
            },
            "7_day_forecast": {
                "expected_cases": int(current_cases * (base_growth ** 7)),
                "confidence": "medium" if confidence >= 0.6 else "low"
            },
            "peak_prediction": {
                "estimated_peak_date": (datetime.now() + timedelta(days=10)).strftime("%Y-%m-%d"),
                "estimated_peak_cases": int(current_cases * (base_growth ** 10))
            }
        }
    
    def _generate_comprehensive_recommendations(self, analysis: Dict[str, Any], symptom_reports: List[SymptomReportRequest], absence_reports: List[AbsenceReportRequest]) -> List[str]:
        """Comprehensive recommendation engine"""
        recommendations = []
        
        total_affected = len(set([r.student_id for r in symptom_reports] + [r.student_id for r in absence_reports]))
        confidence = analysis.get("confidence_score", 0)
        
        if confidence >= 0.8 or total_affected >= 10:
            recommendations.extend([
                "Implement immediate containment protocols",
                "Contact local health authorities within 24 hours",
                "Consider temporary school closure",
                "Begin comprehensive contact tracing"
            ])
        elif confidence >= 0.6 or total_affected >= 5:
            recommendations.extend([
                "Enhance monitoring and surveillance",
                "Increase cleaning and disinfection protocols",
                "Implement social distancing measures",
                "Prepare parent communication materials"
            ])
        else:
            recommendations.extend([
                "Continue routine monitoring",
                "Maintain standard hygiene protocols",
                "Document all cases for trend analysis"
            ])
        
        return recommendations[:6]

    def get_surveillance_summary(self) -> Dict[str, Any]:
        """Get summary of current surveillance data"""
        try:
            total_symptoms = len(self.symptom_reports)
            total_absences = len(self.absence_records)

            # Recent activity (last 7 days)
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=7)

            recent_symptoms = [
                s for s in self.symptom_reports
                if datetime.strptime(s.date_reported, '%Y-%m-%d') >= cutoff_date
            ]
            recent_absences = [
                a for a in self.absence_records
                if datetime.strptime(a.date, '%Y-%m-%d') >= cutoff_date
            ]

            return {
                "success": True,
                "surveillance_summary": {
                    "total_symptom_reports": total_symptoms,
                    "total_absence_records": total_absences,
                    "recent_symptoms_7_days": len(recent_symptoms),
                    "recent_absences_7_days": len(recent_absences),
                    "monitoring_status": "active" if total_symptoms > 0 or total_absences > 0 else "inactive",
                    "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def clear_surveillance_data(self) -> Dict[str, Any]:
        """Clear all stored surveillance data"""
        try:
            symptom_count = len(self.symptom_reports)
            absence_count = len(self.absence_records)

            self.symptom_reports.clear()
            self.absence_records.clear()

            return {
                "success": True,
                "message": "Surveillance data cleared successfully",
                "cleared_data": {
                    "symptom_reports_cleared": symptom_count,
                    "absence_records_cleared": absence_count,
                    "total_records_cleared": symptom_count + absence_count
                }
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

# Create tool instance
outbreak_detector_tool = OutbreakDetectorTool()
