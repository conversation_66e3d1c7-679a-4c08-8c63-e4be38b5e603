"""
Wellness Center Package

A comprehensive wellness center management system that provides analytics and insights
for healthcare administrators and doctors.

This package contains:
- Unified Lab Manager (Dashboard & Task Summary)
- Auto Test Result Explainer
- Child Growth Tracker (Pediatric Development Monitoring)
- Outbreak Detector (Disease Surveillance & Early Warning)
- Health Analytics Tools
- Quality Control Systems
"""

from .tools_unified_lab_manager import UnifiedLabManagerTool, UnifiedLabRequest
from .tools_auto_test_explainer import auto_test_explainer_tool, AutoTestExplainerRequest
from .tools_child_growth_tracker import (
    ChildGrowthTrackerTool,
    child_growth_tracker_tool,
    ChildProfileRequest,
    GrowthMeasurementRequest,
    MilestoneRequest,
    VaccinationRequest,
    GrowthAnalysisRequest,
    GrowthPredictionRequest,
    PhotoMemoryRequest,
    DoctorNoteRequest,
    ReminderRequest,
    FullReportRequest
)
from .tools_outbreak_detector import (
    OutbreakDetectorTool,
    outbreak_detector_tool,
    OutbreakAnalysisRequest,
    SymptomReportRequest,
    AbsenceReportRequest,
    ComprehensiveOutbreakRequest
)

__version__ = "1.0.0"
__author__ = "Wellness Center Team"

__all__ = [
    "UnifiedLabManagerTool",
    "UnifiedLabRequest",
    "auto_test_explainer_tool",
    "AutoTestExplainerRequest",
    "ChildGrowthTrackerTool",
    "child_growth_tracker_tool",
    "ChildProfileRequest",
    "GrowthMeasurementRequest",
    "MilestoneRequest",
    "VaccinationRequest",
    "GrowthAnalysisRequest",
    "GrowthPredictionRequest",
    "PhotoMemoryRequest",
    "DoctorNoteRequest",
    "ReminderRequest",
    "FullReportRequest",
    "OutbreakDetectorTool",
    "outbreak_detector_tool",
    "OutbreakAnalysisRequest",
    "SymptomReportRequest",
    "AbsenceReportRequest",
    "ComprehensiveOutbreakRequest"
]
